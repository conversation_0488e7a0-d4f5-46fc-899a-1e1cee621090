"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = authRoutes;
const express_1 = require("express");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const auth_validation_1 = require("../validation/auth.validation");
/**
 * Authentication routes
 * @param authController Authentication controller
 * @returns Express router
 */
function authRoutes(authController) {
    const router = (0, express_1.Router)();
    // Public routes
    router.post('/register', (0, validation_middleware_1.validateRequest)(auth_validation_1.authValidation.register), authController.register);
    router.post('/login', (0, validation_middleware_1.validateRequest)(auth_validation_1.authValidation.login), authController.login);
    router.post('/refresh-token', (0, validation_middleware_1.validateRequest)(auth_validation_1.authValidation.refreshToken), authController.refreshToken);
    router.post('/logout', authController.logout);
    // Protected routes
    router.get('/profile', auth_middleware_1.authMiddleware, authController.getUserProfile);
    router.get('/sessions', auth_middleware_1.authMiddleware, authController.getActiveSessions);
    router.get('/sessions/current', authController.getCurrentSession);
    router.delete('/sessions/:sessionId', auth_middleware_1.authMiddleware, authController.revokeSession);
    router.post('/logout-all-other', auth_middleware_1.authMiddleware, authController.logoutFromAllOtherSessions);
    router.post('/logout-all', auth_middleware_1.authMiddleware, authController.logoutFromAllSessions);
    // Enhanced multi-device session management routes
    router.get('/device-sessions', auth_middleware_1.authMiddleware, authController.getDeviceSessions);
    router.post('/logout-device-type/:deviceType', auth_middleware_1.authMiddleware, authController.logoutFromDeviceType);
    router.get('/session-statistics', auth_middleware_1.authMiddleware, authController.getSessionStatistics);
    router.post('/cleanup-sessions', auth_middleware_1.authMiddleware, authController.validateAndCleanupSessions);
    router.post('/force-security-sync', auth_middleware_1.authMiddleware, authController.forceSecuritySync);
    // Two-Factor Authentication routes
    router.post('/2fa/enable', auth_middleware_1.authMiddleware, authController.enableTwoFactor);
    router.post('/2fa/verify-enable', auth_middleware_1.authMiddleware, authController.verifyAndEnableTwoFactor);
    router.post('/2fa/disable', auth_middleware_1.authMiddleware, authController.disableTwoFactor);
    router.get('/2fa/status', auth_middleware_1.authMiddleware, authController.getTwoFactorStatus);
    router.post('/2fa/regenerate-backup-codes', auth_middleware_1.authMiddleware, authController.regenerateBackupCodes);
    return router;
}
//# sourceMappingURL=auth.routes.js.map