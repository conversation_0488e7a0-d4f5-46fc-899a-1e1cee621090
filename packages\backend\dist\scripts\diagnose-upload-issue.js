#!/usr/bin/env ts-node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const storage_service_1 = require("../services/storage.service");
const file_service_1 = require("../services/file.service");
const crypto = __importStar(require("crypto"));
async function diagnoseUploadIssue() {
    console.log('🔍 开始诊断文件上传问题...\n');
    // 1. 检查存储服务健康状态
    console.log('1. 检查存储节点健康状态:');
    try {
        const healthyNodes = storage_service_1.storageService.getHealthyNodes();
        console.log(`   ✅ 健康节点数量: ${healthyNodes.length}`);
        if (healthyNodes.length === 0) {
            console.log('   ❌ 没有健康的存储节点！');
            console.log('   💡 请检查 MinIO 服务是否正在运行');
            console.log('   💡 运行命令: docker-compose -f docker-compose.dev.yml up -d minio');
            return;
        }
        healthyNodes.forEach(node => {
            console.log(`   - 节点 ${node.id}: ${node.endPoint}:${node.port} (健康: ${node.isHealthy})`);
        });
    }
    catch (error) {
        console.log(`   ❌ 存储服务检查失败: ${error}`);
        return;
    }
    // 2. 测试存储连接
    console.log('\n2. 测试存储连接:');
    try {
        const testFileId = crypto.randomUUID();
        const testBuffer = Buffer.from('测试文件内容');
        console.log('   📤 尝试上传测试文件...');
        const fileChunks = await storage_service_1.storageService.uploadFileChunked(testFileId, testBuffer, {
            originalName: 'test.txt',
            mimeType: 'text/plain'
        });
        console.log('   ✅ 文件上传成功');
        console.log(`   📊 文件信息: ${fileChunks.totalChunks} 个块, 总大小: ${fileChunks.totalSize} 字节`);
        // 3. 测试文件下载
        console.log('\n3. 测试文件下载:');
        const downloadedBuffer = await storage_service_1.storageService.downloadFileChunked(fileChunks);
        if (downloadedBuffer.equals(testBuffer)) {
            console.log('   ✅ 文件下载成功，内容一致');
        }
        else {
            console.log('   ❌ 文件下载失败，内容不一致');
        }
        // 4. 清理测试文件
        console.log('\n4. 清理测试文件:');
        try {
            await storage_service_1.storageService.deleteFile(testFileId);
            console.log('   ✅ 测试文件清理完成');
        }
        catch (error) {
            console.log(`   ⚠️  测试文件清理失败: ${error}`);
        }
    }
    catch (error) {
        console.log(`   ❌ 存储测试失败: ${error}`);
        if (error instanceof Error) {
            if (error.message.includes('ECONNREFUSED')) {
                console.log('   💡 连接被拒绝，请检查 MinIO 服务是否运行');
                console.log('   💡 运行命令: docker-compose -f docker-compose.dev.yml up -d minio');
            }
            else if (error.message.includes('Access Denied')) {
                console.log('   💡 访问被拒绝，请检查 MinIO 访问密钥配置');
            }
            else if (error.message.includes('bucket')) {
                console.log('   💡 存储桶问题，MinIO 会自动创建存储桶');
            }
        }
        return;
    }
    // 5. 检查文件服务
    console.log('\n5. 检查文件服务:');
    try {
        // 模拟用户上传
        const mockUserId = 'test-user-' + crypto.randomUUID();
        const mockFileUpload = {
            file: Buffer.from('模拟文件内容'),
            filename: 'test-upload.txt',
            mimeType: 'text/plain',
            tags: ['test']
        };
        console.log('   📤 测试文件服务上传...');
        const fileInfo = await file_service_1.fileService.uploadFile(mockUserId, mockFileUpload, {});
        console.log('   ✅ 文件服务上传成功');
        console.log(`   📄 文件信息: ${fileInfo.filename} (${fileInfo.size} 字节)`);
    }
    catch (error) {
        console.log(`   ❌ 文件服务测试失败: ${error}`);
        if (error instanceof Error) {
            if (error.message.includes('database')) {
                console.log('   💡 数据库连接问题，请检查 PostgreSQL 和 MongoDB 服务');
                console.log('   💡 运行命令: docker-compose -f docker-compose.dev.yml up -d postgres mongodb');
            }
        }
        return;
    }
    console.log('\n🎉 所有测试通过！文件上传功能应该正常工作。');
    console.log('\n如果仍然遇到问题，请检查:');
    console.log('- 前端请求格式是否正确');
    console.log('- 认证令牌是否有效');
    console.log('- 文件大小是否超过限制');
    console.log('- 文件类型是否被允许');
}
// 运行诊断
diagnoseUploadIssue().catch(error => {
    console.error('诊断过程中发生错误:', error);
    process.exit(1);
});
//# sourceMappingURL=diagnose-upload-issue.js.map