"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const db_1 = require("./utils/db");
const user_dao_1 = require("./dao/user.dao");
const session_dao_1 = require("./dao/session.dao");
const auth_service_1 = require("./services/auth.service");
const auth_controller_1 = require("./controllers/auth.controller");
const storage_service_1 = require("./services/storage.service");
const cdn_service_1 = require("./services/cdn.service");
const image_service_1 = require("./services/image.service");
const image_controller_1 = require("./controllers/image.controller");
const auth_routes_1 = require("./routes/auth.routes");
const file_routes_1 = __importDefault(require("./routes/file.routes"));
const image_routes_1 = require("./routes/image.routes");
const websocket_middleware_1 = require("./middleware/websocket.middleware");
const logger_1 = __importDefault(require("./utils/logger"));
/**
 * Create Express application
 * @returns Express application
 */
function createApp() {
    const app = (0, express_1.default)();
    // Middleware
    app.use((0, cors_1.default)({
        origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
            'Access-Control-Request-Method',
            'Access-Control-Request-Headers'
        ],
        exposedHeaders: ['Set-Cookie'],
        optionsSuccessStatus: 200,
    }));
    app.use((0, helmet_1.default)());
    app.use((0, compression_1.default)());
    app.use(express_1.default.json());
    app.use(express_1.default.urlencoded({ extended: true }));
    app.use((0, cookie_parser_1.default)());
    app.use((0, morgan_1.default)('combined', {
        stream: {
            write: (message) => logger_1.default.http(message.trim()),
        },
    }));
    // Create DAOs
    const userDAO = new user_dao_1.UserDAO(db_1.pgPool);
    const sessionDAO = new session_dao_1.SessionDAO(db_1.pgPool);
    // Create services
    const authService = new auth_service_1.AuthService(userDAO, sessionDAO);
    const storageConfig = {
        nodes: [{
                id: 'node-1',
                endPoint: process.env.MINIO_ENDPOINT || 'localhost',
                port: parseInt(process.env.MINIO_PORT || '9000', 10),
                useSSL: process.env.MINIO_USE_SSL === 'true',
                accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
                secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
                region: process.env.MINIO_REGION || 'us-east-1',
                isHealthy: true
            }],
        defaultBucket: process.env.MINIO_BUCKET || 'cloud-storage',
        chunkSize: parseInt(process.env.CHUNK_SIZE || '5242880', 10),
        replicationFactor: parseInt(process.env.REPLICATION_FACTOR || '2', 10)
    };
    const storageService = new storage_service_1.StorageService(storageConfig);
    // CDN configuration
    const cdnConfig = {
        provider: process.env.CDN_PROVIDER || 'local',
        baseUrl: process.env.CDN_BASE_URL || 'http://localhost:3000',
        apiKey: process.env.CDN_API_KEY,
        secretKey: process.env.CDN_SECRET_KEY,
        zoneId: process.env.CDN_ZONE_ID,
        distributionId: process.env.CDN_DISTRIBUTION_ID,
        cacheTtl: parseInt(process.env.CDN_CACHE_TTL || '3600', 10),
        purgeEndpoint: process.env.CDN_PURGE_ENDPOINT
    };
    const cdnService = new cdn_service_1.CDNService(db_1.mongoClient, cdnConfig);
    const imageService = new image_service_1.ImageService(db_1.mongoClient, storageService, cdnService);
    // Create controllers
    const authController = new auth_controller_1.AuthController(authService);
    const imageController = new image_controller_1.ImageController(imageService);
    // WebSocket middleware
    app.use(websocket_middleware_1.injectWebSocketService);
    // Routes
    app.use('/api/auth', (0, auth_routes_1.authRoutes)(authController));
    app.use('/api/files', file_routes_1.default);
    app.use('/api/images', (0, image_routes_1.createImageRoutes)(imageController));
    // Health check endpoint
    app.get('/health', (_req, res) => {
        res.status(200).json({ status: 'ok' });
    });
    // API health check endpoint
    app.get('/api/health', (_req, res) => {
        res.status(200).json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            cors: 'enabled'
        });
    });
    // Error handling middleware
    app.use((err, _req, res, _next) => {
        logger_1.default.error(err.stack);
        res.status(500).json({
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? err.message : undefined,
        });
    });
    return app;
}
//# sourceMappingURL=app.js.map