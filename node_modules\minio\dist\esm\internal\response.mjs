export async function readAsBuffer(res) {
  return new Promise((resolve, reject) => {
    const body = [];
    res.on('data', chunk => body.push(chunk)).on('error', e => reject(e)).on('end', () => resolve(Buffer.concat(body)));
  });
}
export async function readAsString(res) {
  const body = await readAsBuffer(res);
  return body.toString();
}
export async function drainResponse(res) {
  return new Promise((resolve, reject) => {
    res.on('data', () => {}).on('error', e => reject(e)).on('end', () => resolve());
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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