"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
exports.connectRedis = connectRedis;
exports.getRedisClient = getRedisClient;
exports.closeRedis = closeRedis;
const redis_1 = require("redis");
const logger_1 = require("../utils/logger");
let redisClient = null;
async function connectRedis() {
    try {
        const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
        redisClient = (0, redis_1.createClient)({
            url: redisUrl,
            socket: {
                connectTimeout: 5000,
            },
        });
        redisClient.on('error', error => {
            logger_1.logger.error('Redis client error:', error);
        });
        redisClient.on('connect', () => {
            logger_1.logger.info('Redis client connected');
        });
        redisClient.on('ready', () => {
            logger_1.logger.info('Redis client ready');
        });
        redisClient.on('end', () => {
            logger_1.logger.info('Redis client disconnected');
        });
        await redisClient.connect();
        // Test connection
        await redisClient.ping();
        logger_1.logger.info('Redis connected successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to Redis:', error);
        throw error;
    }
}
function getRedisClient() {
    if (!redisClient) {
        throw new Error('Redis client not initialized');
    }
    return redisClient;
}
async function closeRedis() {
    try {
        if (redisClient) {
            await redisClient.quit();
            redisClient = null;
            logger_1.logger.info('Redis connection closed');
        }
    }
    catch (error) {
        logger_1.logger.error('Error closing Redis connection:', error);
    }
}
// Cache utility functions
class CacheService {
    static initialize(client) {
        this.client = client;
    }
    static async get(key) {
        try {
            return await this.client.get(key);
        }
        catch (error) {
            logger_1.logger.error(`Cache get error for key ${key}:`, error);
            return null;
        }
    }
    static async set(key, value, ttlSeconds) {
        try {
            if (ttlSeconds) {
                await this.client.setEx(key, ttlSeconds, value);
            }
            else {
                await this.client.set(key, value);
            }
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Cache set error for key ${key}:`, error);
            return false;
        }
    }
    static async del(key) {
        try {
            await this.client.del(key);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Cache delete error for key ${key}:`, error);
            return false;
        }
    }
    static async exists(key) {
        try {
            const result = await this.client.exists(key);
            return result === 1;
        }
        catch (error) {
            logger_1.logger.error(`Cache exists error for key ${key}:`, error);
            return false;
        }
    }
}
exports.CacheService = CacheService;
// Graceful shutdown
process.on('SIGINT', async () => {
    logger_1.logger.info('Received SIGINT, closing Redis connection...');
    await closeRedis();
});
process.on('SIGTERM', async () => {
    logger_1.logger.info('Received SIGTERM, closing Redis connection...');
    await closeRedis();
});
//# sourceMappingURL=redis.js.map