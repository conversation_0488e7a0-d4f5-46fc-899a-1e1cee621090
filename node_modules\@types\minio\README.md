# Installation
> `npm install --save @types/minio`

# Summary
This package contains type definitions for minio (https://github.com/minio/minio-js#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minio.

### Additional Details
 * Last updated: Mon, 22 May 2023 19:32:54 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/barinbritva), [<PERSON><PERSON><PERSON>](https://github.com/castorw), [Panagiot<PERSON>](https://github.com/loremaps), [<PERSON>](https://github.com/OutdatedVersion), [<PERSON><PERSON><PERSON>](https://github.com/seohyun0120), and [Trim21](https://github.com/trim21).
