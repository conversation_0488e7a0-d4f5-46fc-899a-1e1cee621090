"use strict";var br=require("tty"),vr=require("./package-70ca1027.cjs"),at=require("./pkgroll_create-require-3c9491e9.cjs"),_u=require("url"),Br=require("child_process"),Sr=require("path"),$r=require("fs"),Tr=require("./node-features-ecef9e7b.cjs"),mu=require("os"),xr=require("events"),Or=require("util"),Nr=require("stream");require("module");function se(t){return t&&typeof t=="object"&&"default"in t?t:{default:t}}var Hr=se(br),Pr=se(Br),K=se(Sr),De=se($r),Au=se(mu),Lr=se(xr),me=se(Or),Ir=se(Nr);const kr="known-flag",Mr="unknown-flag",Wr="argument",{stringify:Ae}=JSON,Gr=/\B([A-Z])/g,jr=t=>t.replace(Gr,"-$1").toLowerCase(),{hasOwnProperty:Ur}=Object.prototype,ye=(t,e)=>Ur.call(t,e),Kr=t=>Array.isArray(t),yu=t=>typeof t=="function"?[t,!1]:Kr(t)?[t[0],!0]:yu(t.type),Vr=(t,e)=>t===Boolean?e!=="false":e,zr=(t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),Yr=/[\s.:=]/,qr=t=>{const e=`Flag name ${Ae(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(Yr);if(u)throw new Error(`${e} cannot contain ${Ae(u==null?void 0:u[0])}`)},Xr=t=>{const e={},u=(n,r)=>{if(ye(e,n))throw new Error(`Duplicate flags named ${Ae(n)}`);e[n]=r};for(const n in t){if(!ye(t,n))continue;qr(n);const r=t[n],s=[[],...yu(r),r];u(n,s);const i=jr(n);if(n!==i&&u(i,s),"alias"in r&&typeof r.alias=="string"){const{alias:D}=r,o=`Flag alias ${Ae(D)} for flag ${Ae(n)}`;if(D.length===0)throw new Error(`${o} cannot be empty`);if(D.length>1)throw new Error(`${o} must be a single character`);u(D,s)}}return e},Qr=(t,e)=>{const u={};for(const n in t){if(!ye(t,n))continue;const[r,,s,i]=e[n];if(r.length===0&&"default"in i){let{default:D}=i;typeof D=="function"&&(D=D()),u[n]=D}else u[n]=s?r:r.pop()}return u},ke="--",Zr=/[.:=]/,Jr=/^-{1,2}\w/,es=t=>{if(!Jr.test(t))return;const e=!t.startsWith(ke);let u=t.slice(e?1:2),n;const r=u.match(Zr);if(r){const{index:s}=r;n=u.slice(s+1),u=u.slice(0,s)}return[u,n,e]},ts=(t,{onFlag:e,onArgument:u})=>{let n;const r=(s,i)=>{if(typeof n!="function")return!0;n(s,i),n=void 0};for(let s=0;s<t.length;s+=1){const i=t[s];if(i===ke){r();const o=t.slice(s+1);u==null||u(o,[s],!0);break}const D=es(i);if(D){if(r(),!e)continue;const[o,a,c]=D;if(c)for(let f=0;f<o.length;f+=1){r();const l=f===o.length-1;n=e(o[f],l?a:void 0,[s,f+1,l])}else n=e(o,a,[s])}else r(i,[s])&&(u==null||u([i],[s]))}r()},us=(t,e)=>{for(const[u,n,r]of e.reverse()){if(n){const s=t[u];let i=s.slice(0,n);if(r||(i+=s.slice(n+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},wu=(t,e=process.argv.slice(2),{ignore:u}={})=>{const n=[],r=Xr(t),s={},i=[];return i[ke]=[],ts(e,{onFlag(D,o,a){const c=ye(r,D);if(!(u!=null&&u(c?kr:Mr,D,o))){if(c){const[f,l]=r[D],p=Vr(l,o),C=(F,A)=>{n.push(a),A&&n.push(A),f.push(zr(l,F||""))};return p===void 0?C:C(p)}ye(s,D)||(s[D]=[]),s[D].push(o===void 0?!0:o),n.push(a)}},onArgument(D,o,a){u!=null&&u(Wr,e[o[0]])||(i.push(...D),a?(i[ke]=D,e.splice(o[0])):n.push(o))}}),us(e,n),{flags:Qr(t,r),unknownFlags:s,_:i}};var ns=Object.create,Me=Object.defineProperty,rs=Object.defineProperties,ss=Object.getOwnPropertyDescriptor,is=Object.getOwnPropertyDescriptors,Ds=Object.getOwnPropertyNames,Ru=Object.getOwnPropertySymbols,os=Object.getPrototypeOf,bu=Object.prototype.hasOwnProperty,as=Object.prototype.propertyIsEnumerable,vu=(t,e,u)=>e in t?Me(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,We=(t,e)=>{for(var u in e||(e={}))bu.call(e,u)&&vu(t,u,e[u]);if(Ru)for(var u of Ru(e))as.call(e,u)&&vu(t,u,e[u]);return t},lt=(t,e)=>rs(t,is(e)),ls=t=>Me(t,"__esModule",{value:!0}),cs=(t,e)=>()=>(t&&(e=t(t=0)),e),fs=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),hs=(t,e,u,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Ds(e))!bu.call(t,r)&&(u||r!=="default")&&Me(t,r,{get:()=>e[r],enumerable:!(n=ss(e,r))||n.enumerable});return t},ds=(t,e)=>hs(ls(Me(t!=null?ns(os(t)):{},"default",!e&&t&&t.__esModule?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t),U=cs(()=>{}),Es=fs((t,e)=>{U(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});U(),U(),U();var ps=t=>{var e,u,n;let r=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(r)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:r}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(n=t.stdoutColumns)!=null?n:r}};U(),U(),U(),U(),U();function Cs({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}function Bu(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(Cs(),"")}U();function Fs(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}var gs=ds(Es(),1);function oe(t){if(typeof t!="string"||t.length===0||(t=Bu(t),t.length===0))return 0;t=t.replace((0,gs.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let n=t.codePointAt(u);n<=31||n>=127&&n<=159||n>=768&&n<=879||(n>65535&&u++,e+=Fs(n)?2:1)}return e}var Su=t=>Math.max(...t.split(`
`).map(oe)),_s=t=>{let e=[];for(let u of t){let{length:n}=u,r=n-e.length;for(let s=0;s<r;s+=1)e.push(0);for(let s=0;s<n;s+=1){let i=Su(u[s]);i>e[s]&&(e[s]=i)}}return e};U();var $u=/^\d+%$/,Tu={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},ms=(t,e)=>{var u;let n=[];for(let r=0;r<t.length;r+=1){let s=(u=e[r])!=null?u:"auto";if(typeof s=="number"||s==="auto"||s==="content-width"||typeof s=="string"&&$u.test(s)){n.push(lt(We({},Tu),{width:s,contentWidth:t[r]}));continue}if(s&&typeof s=="object"){let i=lt(We(We({},Tu),s),{contentWidth:t[r]});i.horizontalPadding=i.paddingLeft+i.paddingRight,n.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(s)}`)}return n};function As(t,e){for(let u of t){let{width:n}=u;if(n==="content-width"&&(u.width=u.contentWidth),n==="auto"){let o=Math.min(20,u.contentWidth);u.width=o,u.autoOverflow=u.contentWidth-o}if(typeof n=="string"&&$u.test(n)){let o=Number.parseFloat(n.slice(0,-1))/100;u.width=Math.floor(e*o)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:r}=u,s=1,i=s+r;if(i>=e){let o=i-e,a=Math.ceil(u.paddingLeft/r*o),c=o-a;u.paddingLeft-=a,u.paddingRight-=c,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let D=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,D),s)}}var xu=()=>Object.assign([],{columns:0});function ys(t,e){let u=[xu()],[n]=u;for(let r of t){let s=r.width+r.horizontalPadding;n.columns+s>e&&(n=xu(),u.push(n)),n.push(r),n.columns+=s}for(let r of u){let s=r.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-s;if(i===0)continue;let D=r.filter(l=>"autoOverflow"in l),o=D.filter(l=>l.autoOverflow>0),a=o.reduce((l,p)=>l+p.autoOverflow,0),c=Math.min(a,i);for(let l of o){let p=Math.floor(l.autoOverflow/a*c);l.width+=p,i-=p}let f=Math.floor(i/D.length);for(let l=0;l<D.length;l+=1){let p=D[l];l===D.length-1?p.width+=i:p.width+=f,i-=f}}return u}function ws(t,e,u){let n=ms(u,e);return As(n,t),ys(n,t)}U(),U(),U();var ct=10,Ou=(t=0)=>e=>`\x1B[${e+t}m`,Nu=(t=0)=>e=>`\x1B[${38+t};5;${e}m`,Hu=(t=0)=>(e,u,n)=>`\x1B[${38+t};2;${e};${u};${n}m`;function Rs(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,n]of Object.entries(e)){for(let[r,s]of Object.entries(n))e[r]={open:`\x1B[${s[0]}m`,close:`\x1B[${s[1]}m`},n[r]=e[r],t.set(s[0],s[1]);Object.defineProperty(e,u,{value:n,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=Ou(),e.color.ansi256=Nu(),e.color.ansi16m=Hu(),e.bgColor.ansi=Ou(ct),e.bgColor.ansi256=Nu(ct),e.bgColor.ansi16m=Hu(ct),Object.defineProperties(e,{rgbToAnsi256:{value:(u,n,r)=>u===n&&n===r?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:u=>{let n=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!n)return[0,0,0];let{colorString:r}=n.groups;r.length===3&&(r=r.split("").map(i=>i+i).join(""));let s=Number.parseInt(r,16);return[s>>16&255,s>>8&255,s&255]},enumerable:!1},hexToAnsi256:{value:u=>e.rgbToAnsi256(...e.hexToRgb(u)),enumerable:!1},ansi256ToAnsi:{value:u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let n,r,s;if(u>=232)n=((u-232)*10+8)/255,r=n,s=n;else{u-=16;let o=u%36;n=Math.floor(u/36)/5,r=Math.floor(o/6)/5,s=o%6/5}let i=Math.max(n,r,s)*2;if(i===0)return 30;let D=30+(Math.round(s)<<2|Math.round(r)<<1|Math.round(n));return i===2&&(D+=60),D},enumerable:!1},rgbToAnsi:{value:(u,n,r)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,n,r)),enumerable:!1},hexToAnsi:{value:u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),enumerable:!1}}),e}var bs=Rs(),vs=bs,Ge=new Set(["\x1B","\x9B"]),Bs=39,ft="\x07",Pu="[",Ss="]",Lu="m",ht=`${Ss}8;;`,Iu=t=>`${Ge.values().next().value}${Pu}${t}${Lu}`,ku=t=>`${Ge.values().next().value}${ht}${t}${ft}`,$s=t=>t.split(" ").map(e=>oe(e)),dt=(t,e,u)=>{let n=[...e],r=!1,s=!1,i=oe(Bu(t[t.length-1]));for(let[D,o]of n.entries()){let a=oe(o);if(i+a<=u?t[t.length-1]+=o:(t.push(o),i=0),Ge.has(o)&&(r=!0,s=n.slice(D+1).join("").startsWith(ht)),r){s?o===ft&&(r=!1,s=!1):o===Lu&&(r=!1);continue}i+=a,i===u&&D<n.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},Ts=t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(oe(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},xs=(t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let n="",r,s,i=$s(t),D=[""];for(let[a,c]of t.split(" ").entries()){u.trim!==!1&&(D[D.length-1]=D[D.length-1].trimStart());let f=oe(D[D.length-1]);if(a!==0&&(f>=e&&(u.wordWrap===!1||u.trim===!1)&&(D.push(""),f=0),(f>0||u.trim===!1)&&(D[D.length-1]+=" ",f++)),u.hard&&i[a]>e){let l=e-f,p=1+Math.floor((i[a]-l-1)/e);Math.floor((i[a]-1)/e)<p&&D.push(""),dt(D,c,e);continue}if(f+i[a]>e&&f>0&&i[a]>0){if(u.wordWrap===!1&&f<e){dt(D,c,e);continue}D.push("")}if(f+i[a]>e&&u.wordWrap===!1){dt(D,c,e);continue}D[D.length-1]+=c}u.trim!==!1&&(D=D.map(a=>Ts(a)));let o=[...D.join(`
`)];for(let[a,c]of o.entries()){if(n+=c,Ge.has(c)){let{groups:l}=new RegExp(`(?:\\${Pu}(?<code>\\d+)m|\\${ht}(?<uri>.*)${ft})`).exec(o.slice(a).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);r=p===Bs?void 0:p}else l.uri!==void 0&&(s=l.uri.length===0?void 0:l.uri)}let f=vs.codes.get(Number(r));o[a+1]===`
`?(s&&(n+=ku("")),r&&f&&(n+=Iu(f))):c===`
`&&(r&&f&&(n+=Iu(r)),s&&(n+=ku(s)))}return n};function Os(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(n=>xs(n,e,u)).join(`
`)}var Mu=t=>Array.from({length:t}).fill("");function Ns(t,e){let u=[],n=0;for(let r of t){let s=0,i=r.map(o=>{var a;let c=(a=e[n])!=null?a:"";n+=1,o.preprocess&&(c=o.preprocess(c)),Su(c)>o.width&&(c=Os(c,o.width,{hard:!0}));let f=c.split(`
`);if(o.postprocess){let{postprocess:l}=o;f=f.map((p,C)=>l.call(o,p,C))}return o.paddingTop&&f.unshift(...Mu(o.paddingTop)),o.paddingBottom&&f.push(...Mu(o.paddingBottom)),f.length>s&&(s=f.length),lt(We({},o),{lines:f})}),D=[];for(let o=0;o<s;o+=1){let a=i.map(c=>{var f;let l=(f=c.lines[o])!=null?f:"",p=Number.isFinite(c.width)?" ".repeat(c.width-oe(l)):"",C=c.paddingLeftString;return c.align==="right"&&(C+=p),C+=l,c.align==="left"&&(C+=p),C+c.paddingRightString}).join("");D.push(a)}u.push(D.join(`
`))}return u.join(`
`)}function Hs(t,e){if(!t||t.length===0)return"";let u=_s(t),n=u.length;if(n===0)return"";let{stdoutColumns:r,columns:s}=ps(e);if(s.length>n)throw new Error(`${s.length} columns defined, but only ${n} columns found`);let i=ws(r,s,u);return t.map(D=>Ns(i,D)).join(`
`)}U();var Ps=["<",">","=",">=","<="];function Ls(t){if(!Ps.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}function Is(t){let e=Object.keys(t).map(u=>{let[n,r]=u.split(" ");Ls(n);let s=Number.parseInt(r,10);if(Number.isNaN(s))throw new TypeError(`Invalid breakpoint value: ${r}`);let i=t[u];return{operator:n,breakpoint:s,value:i}}).sort((u,n)=>n.breakpoint-u.breakpoint);return u=>{var n;return(n=e.find(({operator:r,breakpoint:s})=>r==="="&&u===s||r===">"&&u>s||r==="<"&&u<s||r===">="&&u>=s||r==="<="&&u<=s))==null?void 0:n.value}}const ks=t=>t.replace(/[\W_]([a-z\d])?/gi,(e,u)=>u?u.toUpperCase():""),Ms=t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),Ws={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:t=>t.trim()},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function Gs(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((u,n)=>u.localeCompare(n)).map(u=>{const n=t[u],r="alias"in n;return r&&(e=!0),{name:u,flag:n,flagFormatted:`--${Ms(u)}`,aliasesEnabled:e,aliasFormatted:r?`-${n.alias}`:void 0}}).map(u=>(u.aliasesEnabled=e,[{type:"flagName",data:u},{type:"flagDescription",data:u}])),tableBreakpoints:Ws}}}const Wu=t=>{var e;return!t||((e=t.version)!=null?e:t.help?t.help.version:void 0)},Gu=t=>{var u;const e="parent"in t&&((u=t.parent)==null?void 0:u.name);return(e?`${e} `:"")+t.name};function js(t){var n;const e=[];t.name&&e.push(Gu(t));const u=(n=Wu(t))!=null?n:"parent"in t&&Wu(t.parent);if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}function Us(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}function Ks(t){var u;const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const n=[],r=[Gu(t)];if(t.flags&&Object.keys(t.flags).length>0&&r.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:s}=t,i=s.indexOf("--"),D=i>-1&&s.slice(i+1).some(o=>o.startsWith("<"));r.push(s.map(o=>o!=="--"?o:D?"--":"[--]").join(" "))}if(r.length>1&&n.push(r.join(" ")),"commands"in t&&((u=t.commands)!=null&&u.length)&&n.push(`${t.name} <command>`),n.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:n.join(`
`)}}}}function Vs(t){var e;return!("commands"in t)||!((e=t.commands)!=null&&e.length)?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(u=>[u.options.name,u.options.help?u.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}function zs(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:Gs(t.flags),indentBody:0}}}function Ys(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}function qs(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}const Xs=t=>[js,Us,Ks,Vs,zs,Ys,qs].map(e=>e(t)).filter(Boolean),Qs=Hr.default.WriteStream.prototype.hasColors();class Zs{text(e){return e}bold(e){return Qs?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:n=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:n}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:n}){return Hs(e.map(r=>r.map(s=>this.render(s))),n?Is(n):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:n,aliasesEnabled:r,aliasFormatted:s}=e;let i="";if(s?i+=`${s}, `:r&&(i+="    "),i+=n,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const D=this.flagParameter("type"in u?u.type:u);D&&(i+=`${this.flagOperator(e)}${D}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){var n;let u="description"in e&&(n=e.description)!=null?n:"";if("default"in e){let{default:r}=e;typeof r=="function"&&(r=r()),r&&(u+=` (default: ${this.flagDefault(r)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const Et=/^[\w.-]+$/,{stringify:J}=JSON,Js=/[|\\{}()[\]^$+*?.]/;function pt(t){const e=[];let u,n;for(const r of t){if(n)throw new Error(`Invalid parameter: Spread parameter ${J(n)} must be last`);const s=r[0],i=r[r.length-1];let D;if(s==="<"&&i===">"&&(D=!0,u))throw new Error(`Invalid parameter: Required parameter ${J(r)} cannot come after optional parameter ${J(u)}`);if(s==="["&&i==="]"&&(D=!1,u=r),D===void 0)throw new Error(`Invalid parameter: ${J(r)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let o=r.slice(1,-1);const a=o.slice(-3)==="...";a&&(n=r,o=o.slice(0,-3));const c=o.match(Js);if(c)throw new Error(`Invalid parameter: ${J(r)}. Invalid character found ${J(c[0])}`);e.push({name:o,required:D,spread:a})}return e}function Ct(t,e,u,n){for(let r=0;r<e.length;r+=1){const{name:s,required:i,spread:D}=e[r],o=ks(s);if(o in t)throw new Error(`Invalid parameter: ${J(s)} is used more than once.`);const a=D?u.slice(r):u[r];if(D&&(r=e.length),i&&(!a||D&&a.length===0))return console.error(`Error: Missing required parameter ${J(s)}
`),n(),process.exit(1);t[o]=a}}function ei(t){return t===void 0||t!==!1}function ju(t,e,u,n){const r={...e.flags},s=e.version;s&&(r.version={type:Boolean,description:"Show version"});const{help:i}=e,D=ei(i);D&&!("help"in r)&&(r.help={type:Boolean,alias:"h",description:"Show help"});const o=wu(r,n,{ignore:e.ignoreArgv}),a=()=>{console.log(e.version)};if(s&&o.flags.version===!0)return a(),process.exit(0);const c=new Zs,f=D&&(i!=null&&i.render)?i.render:C=>c.render(C),l=C=>{const F=Xs({...e,...C?{help:C}:{},flags:r});console.log(f(F,c))};if(D&&o.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,F=o._;const A=C.indexOf("--"),B=C.slice(A+1),H=Object.create(null);if(A>-1&&B.length>0){C=C.slice(0,A);const S=o._["--"];F=F.slice(0,-S.length||void 0),Ct(H,pt(C),F,l),Ct(H,pt(B),S,l)}else Ct(H,pt(C),F,l);Object.assign(o._,H)}const p={...o,showVersion:a,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}function ti(t,e){const u=new Map;for(const n of e){const r=[n.options.name],{alias:s}=n.options;s&&(Array.isArray(s)?r.push(...s):r.push(s));for(const i of r){if(u.has(i))throw new Error(`Duplicate command name found: ${J(i)}`);u.set(i,n)}}return u.get(t)}function ui(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!Et.test(t.name)))throw new Error(`Invalid script name: ${J(t.name)}`);const n=u[0];if(t.commands&&Et.test(n)){const r=ti(n,t.commands);if(r)return ju(r.options.name,{...r.options,parent:t},r.callback,u.slice(1))}return ju(void 0,t,e,u)}function ni(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!Et.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}var ri=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function si(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var he={exports:{}},Ft,Uu;function ii(){if(Uu)return Ft;Uu=1,Ft=n,n.sync=r;var t=De.default;function e(s,i){var D=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!D||(D=D.split(";"),D.indexOf("")!==-1))return!0;for(var o=0;o<D.length;o++){var a=D[o].toLowerCase();if(a&&s.substr(-a.length).toLowerCase()===a)return!0}return!1}function u(s,i,D){return!s.isSymbolicLink()&&!s.isFile()?!1:e(i,D)}function n(s,i,D){t.stat(s,function(o,a){D(o,o?!1:u(a,s,i))})}function r(s,i){return u(t.statSync(s),s,i)}return Ft}var gt,Ku;function Di(){if(Ku)return gt;Ku=1,gt=e,e.sync=u;var t=De.default;function e(s,i,D){t.stat(s,function(o,a){D(o,o?!1:n(a,i))})}function u(s,i){return n(t.statSync(s),i)}function n(s,i){return s.isFile()&&r(s,i)}function r(s,i){var D=s.mode,o=s.uid,a=s.gid,c=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),f=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),F=l|p,A=D&C||D&p&&a===f||D&l&&o===c||D&F&&c===0;return A}return gt}var je;process.platform==="win32"||ri.TESTING_WINDOWS?je=ii():je=Di();var oi=_t;_t.sync=ai;function _t(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,r){_t(t,e||{},function(s,i){s?r(s):n(i)})})}je(t,e||{},function(n,r){n&&(n.code==="EACCES"||e&&e.ignoreErrors)&&(n=null,r=!1),u(n,r)})}function ai(t,e){try{return je.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}const de=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Vu=K.default,li=de?";":":",zu=oi,Yu=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),qu=(t,e)=>{const u=e.colon||li,n=t.match(/\//)||de&&t.match(/\\/)?[""]:[...de?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],r=de?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",s=de?r.split(u):[""];return de&&t.indexOf(".")!==-1&&s[0]!==""&&s.unshift(""),{pathEnv:n,pathExt:s,pathExtExe:r}},Xu=(t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:n,pathExt:r,pathExtExe:s}=qu(t,e),i=[],D=a=>new Promise((c,f)=>{if(a===n.length)return e.all&&i.length?c(i):f(Yu(t));const l=n[a],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=Vu.join(p,t),F=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;c(o(F,a,0))}),o=(a,c,f)=>new Promise((l,p)=>{if(f===r.length)return l(D(c+1));const C=r[f];zu(a+C,{pathExt:s},(F,A)=>{if(!F&&A)if(e.all)i.push(a+C);else return l(a+C);return l(o(a,c,f+1))})});return u?D(0).then(a=>u(null,a),u):D(0)},ci=(t,e)=>{e=e||{};const{pathEnv:u,pathExt:n,pathExtExe:r}=qu(t,e),s=[];for(let i=0;i<u.length;i++){const D=u[i],o=/^".*"$/.test(D)?D.slice(1,-1):D,a=Vu.join(o,t),c=!o&&/^\.[\\\/]/.test(t)?t.slice(0,2)+a:a;for(let f=0;f<n.length;f++){const l=c+n[f];try{if(zu.sync(l,{pathExt:r}))if(e.all)s.push(l);else return l}catch{}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw Yu(t)};var fi=Xu;Xu.sync=ci;var mt={exports:{}};const Qu=(t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};mt.exports=Qu,mt.exports.default=Qu;var hi=mt.exports;const Zu=K.default,di=fi,Ei=hi;function Ju(t,e){const u=t.options.env||process.env,n=process.cwd(),r=t.options.cwd!=null,s=r&&process.chdir!==void 0&&!process.chdir.disabled;if(s)try{process.chdir(t.options.cwd)}catch{}let i;try{i=di.sync(t.command,{path:u[Ei({env:u})],pathExt:e?Zu.delimiter:void 0})}catch{}finally{s&&process.chdir(n)}return i&&(i=Zu.resolve(r?t.options.cwd:"",i)),i}function pi(t){return Ju(t)||Ju(t,!0)}var Ci=pi,At={};const yt=/([()\][%!^"`<>&|;, *?])/g;function Fi(t){return t=t.replace(yt,"^$1"),t}function gi(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(yt,"^$1"),e&&(t=t.replace(yt,"^$1")),t}At.command=Fi,At.argument=gi;var _i=/^#!(.*)/;const mi=_i;var Ai=(t="")=>{const e=t.match(mi);if(!e)return null;const[u,n]=e[0].replace(/#! ?/,"").split(" "),r=u.split("/").pop();return r==="env"?n:n?`${r} ${n}`:r};const wt=De.default,yi=Ai;function wi(t){const u=Buffer.alloc(150);let n;try{n=wt.openSync(t,"r"),wt.readSync(n,u,0,150,0),wt.closeSync(n)}catch{}return yi(u.toString())}var Ri=wi;const bi=K.default,en=Ci,tn=At,vi=Ri,Bi=process.platform==="win32",Si=/\.(?:com|exe)$/i,$i=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Ti(t){t.file=en(t);const e=t.file&&vi(t.file);return e?(t.args.unshift(t.file),t.command=e,en(t)):t.file}function xi(t){if(!Bi)return t;const e=Ti(t),u=!Si.test(e);if(t.options.forceShell||u){const n=$i.test(e);t.command=bi.normalize(t.command),t.command=tn.command(t.command),t.args=t.args.map(s=>tn.argument(s,n));const r=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${r}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}function Oi(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const n={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?n:xi(n)}var Ni=Oi;const Rt=process.platform==="win32";function bt(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}function Hi(t,e){if(!Rt)return;const u=t.emit;t.emit=function(n,r){if(n==="exit"){const s=un(r,e);if(s)return u.call(t,"error",s)}return u.apply(t,arguments)}}function un(t,e){return Rt&&t===1&&!e.file?bt(e.original,"spawn"):null}function Pi(t,e){return Rt&&t===1&&!e.file?bt(e.original,"spawnSync"):null}var Li={hookChildProcess:Hi,verifyENOENT:un,verifyENOENTSync:Pi,notFoundError:bt};const nn=Pr.default,vt=Ni,Bt=Li;function rn(t,e,u){const n=vt(t,e,u),r=nn.spawn(n.command,n.args,n.options);return Bt.hookChildProcess(r,n),r}function Ii(t,e,u){const n=vt(t,e,u),r=nn.spawnSync(n.command,n.args,n.options);return r.error=r.error||Bt.verifyENOENTSync(r.status,n),r}he.exports=rn,he.exports.spawn=rn,he.exports.sync=Ii,he.exports._parse=vt,he.exports._enoent=Bt;var ki=he.exports,Mi=si(ki);function sn(t,e){const u={...process.env},n=["inherit","inherit","inherit","ipc"];return e&&(e.noCache&&(u.ESBK_DISABLE_CACHE="1"),e.tsconfigPath&&(u.ESBK_TSCONFIG_PATH=e.tsconfigPath)),Mi(process.execPath,["--require",at.require.resolve("./preflight.cjs"),Tr.supportsModuleRegister?"--import":"--loader",_u.pathToFileURL(at.require.resolve("./loader.mjs")).toString(),...t],{stdio:n,env:u})}var Ue={};const Wi=K.default,ee="\\\\/",Dn=`[^${ee}]`,te="\\.",Gi="\\+",ji="\\?",Ke="\\/",Ui="(?=.)",on="[^/]",St=`(?:${Ke}|$)`,an=`(?:^|${Ke})`,$t=`${te}{1,2}${St}`,Ki=`(?!${te})`,Vi=`(?!${an}${$t})`,zi=`(?!${te}{0,1}${St})`,Yi=`(?!${$t})`,qi=`[^.${Ke}]`,Xi=`${on}*?`,ln={DOT_LITERAL:te,PLUS_LITERAL:Gi,QMARK_LITERAL:ji,SLASH_LITERAL:Ke,ONE_CHAR:Ui,QMARK:on,END_ANCHOR:St,DOTS_SLASH:$t,NO_DOT:Ki,NO_DOTS:Vi,NO_DOT_SLASH:zi,NO_DOTS_SLASH:Yi,QMARK_NO_DOT:qi,STAR:Xi,START_ANCHOR:an},Qi={...ln,SLASH_LITERAL:`[${ee}]`,QMARK:Dn,STAR:`${Dn}*?`,DOTS_SLASH:`${te}{1,2}(?:[${ee}]|$)`,NO_DOT:`(?!${te})`,NO_DOTS:`(?!(?:^|[${ee}])${te}{1,2}(?:[${ee}]|$))`,NO_DOT_SLASH:`(?!${te}{0,1}(?:[${ee}]|$))`,NO_DOTS_SLASH:`(?!${te}{1,2}(?:[${ee}]|$))`,QMARK_NO_DOT:`[^.${ee}]`,START_ANCHOR:`(?:^|[${ee}])`,END_ANCHOR:`(?:[${ee}]|$)`},Zi={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Ve={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Zi,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Wi.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?Qi:ln}};(function(t){const e=K.default,u=process.platform==="win32",{REGEX_BACKSLASH:n,REGEX_REMOVE_BACKSLASH:r,REGEX_SPECIAL_CHARS:s,REGEX_SPECIAL_CHARS_GLOBAL:i}=Ve;t.isObject=D=>D!==null&&typeof D=="object"&&!Array.isArray(D),t.hasRegexChars=D=>s.test(D),t.isRegexChar=D=>D.length===1&&t.hasRegexChars(D),t.escapeRegex=D=>D.replace(i,"\\$1"),t.toPosixSlashes=D=>D.replace(n,"/"),t.removeBackslashes=D=>D.replace(r,o=>o==="\\"?"":o),t.supportsLookbehinds=()=>{const D=process.version.slice(1).split(".").map(Number);return D.length===3&&D[0]>=9||D[0]===8&&D[1]>=10},t.isWindows=D=>D&&typeof D.windows=="boolean"?D.windows:u===!0||e.sep==="\\",t.escapeLast=(D,o,a)=>{const c=D.lastIndexOf(o,a);return c===-1?D:D[c-1]==="\\"?t.escapeLast(D,o,c-1):`${D.slice(0,c)}\\${D.slice(c)}`},t.removePrefix=(D,o={})=>{let a=D;return a.startsWith("./")&&(a=a.slice(2),o.prefix="./"),a},t.wrapOutput=(D,o={},a={})=>{const c=a.contains?"":"^",f=a.contains?"":"$";let l=`${c}(?:${D})${f}`;return o.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(Ue);const cn=Ue,{CHAR_ASTERISK:Tt,CHAR_AT:Ji,CHAR_BACKWARD_SLASH:we,CHAR_COMMA:eD,CHAR_DOT:xt,CHAR_EXCLAMATION_MARK:Ot,CHAR_FORWARD_SLASH:fn,CHAR_LEFT_CURLY_BRACE:Nt,CHAR_LEFT_PARENTHESES:Ht,CHAR_LEFT_SQUARE_BRACKET:tD,CHAR_PLUS:uD,CHAR_QUESTION_MARK:hn,CHAR_RIGHT_CURLY_BRACE:nD,CHAR_RIGHT_PARENTHESES:dn,CHAR_RIGHT_SQUARE_BRACKET:rD}=Ve,En=t=>t===fn||t===we,pn=t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},sD=(t,e)=>{const u=e||{},n=t.length-1,r=u.parts===!0||u.scanToEnd===!0,s=[],i=[],D=[];let o=t,a=-1,c=0,f=0,l=!1,p=!1,C=!1,F=!1,A=!1,B=!1,H=!1,S=!1,X=!1,M=!1,ne=0,W,m,b={value:"",depth:0,isGlob:!1};const k=()=>a>=n,E=()=>o.charCodeAt(a+1),x=()=>(W=m,o.charCodeAt(++a));for(;a<n;){m=x();let G;if(m===we){H=b.backslashes=!0,m=x(),m===Nt&&(B=!0);continue}if(B===!0||m===Nt){for(ne++;k()!==!0&&(m=x());){if(m===we){H=b.backslashes=!0,x();continue}if(m===Nt){ne++;continue}if(B!==!0&&m===xt&&(m=x())===xt){if(l=b.isBrace=!0,C=b.isGlob=!0,M=!0,r===!0)continue;break}if(B!==!0&&m===eD){if(l=b.isBrace=!0,C=b.isGlob=!0,M=!0,r===!0)continue;break}if(m===nD&&(ne--,ne===0)){B=!1,l=b.isBrace=!0,M=!0;break}}if(r===!0)continue;break}if(m===fn){if(s.push(a),i.push(b),b={value:"",depth:0,isGlob:!1},M===!0)continue;if(W===xt&&a===c+1){c+=2;continue}f=a+1;continue}if(u.noext!==!0&&(m===uD||m===Ji||m===Tt||m===hn||m===Ot)===!0&&E()===Ht){if(C=b.isGlob=!0,F=b.isExtglob=!0,M=!0,m===Ot&&a===c&&(X=!0),r===!0){for(;k()!==!0&&(m=x());){if(m===we){H=b.backslashes=!0,m=x();continue}if(m===dn){C=b.isGlob=!0,M=!0;break}}continue}break}if(m===Tt){if(W===Tt&&(A=b.isGlobstar=!0),C=b.isGlob=!0,M=!0,r===!0)continue;break}if(m===hn){if(C=b.isGlob=!0,M=!0,r===!0)continue;break}if(m===tD){for(;k()!==!0&&(G=x());){if(G===we){H=b.backslashes=!0,x();continue}if(G===rD){p=b.isBracket=!0,C=b.isGlob=!0,M=!0;break}}if(r===!0)continue;break}if(u.nonegate!==!0&&m===Ot&&a===c){S=b.negated=!0,c++;continue}if(u.noparen!==!0&&m===Ht){if(C=b.isGlob=!0,r===!0){for(;k()!==!0&&(m=x());){if(m===Ht){H=b.backslashes=!0,m=x();continue}if(m===dn){M=!0;break}}continue}break}if(C===!0){if(M=!0,r===!0)continue;break}}u.noext===!0&&(F=!1,C=!1);let $=o,re="",h="";c>0&&(re=o.slice(0,c),o=o.slice(c),f-=c),$&&C===!0&&f>0?($=o.slice(0,f),h=o.slice(f)):C===!0?($="",h=o):$=o,$&&$!==""&&$!=="/"&&$!==o&&En($.charCodeAt($.length-1))&&($=$.slice(0,-1)),u.unescape===!0&&(h&&(h=cn.removeBackslashes(h)),$&&H===!0&&($=cn.removeBackslashes($)));const d={prefix:re,input:t,start:c,base:$,glob:h,isBrace:l,isBracket:p,isGlob:C,isExtglob:F,isGlobstar:A,negated:S,negatedExtglob:X};if(u.tokens===!0&&(d.maxDepth=0,En(m)||i.push(b),d.tokens=i),u.parts===!0||u.tokens===!0){let G;for(let R=0;R<s.length;R++){const Q=G?G+1:c,Z=s[R],V=t.slice(Q,Z);u.tokens&&(R===0&&c!==0?(i[R].isPrefix=!0,i[R].value=re):i[R].value=V,pn(i[R]),d.maxDepth+=i[R].depth),(R!==0||V!=="")&&D.push(V),G=Z}if(G&&G+1<t.length){const R=t.slice(G+1);D.push(R),u.tokens&&(i[i.length-1].value=R,pn(i[i.length-1]),d.maxDepth+=i[i.length-1].depth)}d.slashes=s,d.parts=D}return d};var iD=sD;const ze=Ve,z=Ue,{MAX_LENGTH:Ye,POSIX_REGEX_SOURCE:DD,REGEX_NON_SPECIAL_CHARS:oD,REGEX_SPECIAL_CHARS_BACKREF:aD,REPLACEMENTS:Cn}=ze,lD=(t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(r=>z.escapeRegex(r)).join("..")}return u},Ee=(t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,Pt=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=Cn[t]||t;const u={...e},n=typeof u.maxLength=="number"?Math.min(Ye,u.maxLength):Ye;let r=t.length;if(r>n)throw new SyntaxError(`Input length: ${r}, exceeds maximum allowed length: ${n}`);const s={type:"bos",value:"",output:u.prepend||""},i=[s],D=u.capture?"":"?:",o=z.isWindows(e),a=ze.globChars(o),c=ze.extglobChars(a),{DOT_LITERAL:f,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:F,NO_DOT:A,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:S,QMARK_NO_DOT:X,STAR:M,START_ANCHOR:ne}=a,W=_=>`(${D}(?:(?!${ne}${_.dot?F:f}).)*?)`,m=u.dot?"":A,b=u.dot?S:X;let k=u.bash===!0?W(u):M;u.capture&&(k=`(${k})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const E={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=z.removePrefix(t,E),r=t.length;const x=[],$=[],re=[];let h=s,d;const G=()=>E.index===r-1,R=E.peek=(_=1)=>t[E.index+_],Q=E.advance=()=>t[++E.index]||"",Z=()=>t.slice(E.index+1),V=(_="",T=0)=>{E.consumed+=_,E.index+=T},He=_=>{E.output+=_.output!=null?_.output:_.value,V(_.value)},wr=()=>{let _=1;for(;R()==="!"&&(R(2)!=="("||R(3)==="?");)Q(),E.start++,_++;return _%2===0?!1:(E.negated=!0,E.start++,!0)},Pe=_=>{E[_]++,re.push(_)},ie=_=>{E[_]--,re.pop()},w=_=>{if(h.type==="globstar"){const T=E.braces>0&&(_.type==="comma"||_.type==="brace"),g=_.extglob===!0||x.length&&(_.type==="pipe"||_.type==="paren");_.type!=="slash"&&_.type!=="paren"&&!T&&!g&&(E.output=E.output.slice(0,-h.output.length),h.type="star",h.value="*",h.output=k,E.output+=h.output)}if(x.length&&_.type!=="paren"&&(x[x.length-1].inner+=_.value),(_.value||_.output)&&He(_),h&&h.type==="text"&&_.type==="text"){h.value+=_.value,h.output=(h.output||"")+_.value;return}_.prev=h,i.push(_),h=_},Le=(_,T)=>{const g={...c[T],conditions:1,inner:""};g.prev=h,g.parens=E.parens,g.output=E.output;const y=(u.capture?"(":"")+g.open;Pe("parens"),w({type:_,value:T,output:E.output?"":C}),w({type:"paren",extglob:!0,value:Q(),output:y}),x.push(g)},Rr=_=>{let T=_.close+(u.capture?")":""),g;if(_.type==="negate"){let y=k;if(_.inner&&_.inner.length>1&&_.inner.includes("/")&&(y=W(u)),(y!==k||G()||/^\)+$/.test(Z()))&&(T=_.close=`)$))${y}`),_.inner.includes("*")&&(g=Z())&&/^\.[^\\/.]+$/.test(g)){const O=Pt(g,{...e,fastpaths:!1}).output;T=_.close=`)${O})${y})`}_.prev.type==="bos"&&(E.negatedExtglob=!0)}w({type:"paren",extglob:!0,value:d,output:T}),ie("parens")};if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let _=!1,T=t.replace(aD,(g,y,O,j,P,ot)=>j==="\\"?(_=!0,g):j==="?"?y?y+j+(P?S.repeat(P.length):""):ot===0?b+(P?S.repeat(P.length):""):S.repeat(O.length):j==="."?f.repeat(O.length):j==="*"?y?y+j+(P?k:""):k:y?g:`\\${g}`);return _===!0&&(u.unescape===!0?T=T.replace(/\\/g,""):T=T.replace(/\\+/g,g=>g.length%2===0?"\\\\":g?"\\":"")),T===t&&u.contains===!0?(E.output=t,E):(E.output=z.wrapOutput(T,E,e),E)}for(;!G();){if(d=Q(),d==="\0")continue;if(d==="\\"){const g=R();if(g==="/"&&u.bash!==!0||g==="."||g===";")continue;if(!g){d+="\\",w({type:"text",value:d});continue}const y=/^\\+/.exec(Z());let O=0;if(y&&y[0].length>2&&(O=y[0].length,E.index+=O,O%2!==0&&(d+="\\")),u.unescape===!0?d=Q():d+=Q(),E.brackets===0){w({type:"text",value:d});continue}}if(E.brackets>0&&(d!=="]"||h.value==="["||h.value==="[^")){if(u.posix!==!1&&d===":"){const g=h.value.slice(1);if(g.includes("[")&&(h.posix=!0,g.includes(":"))){const y=h.value.lastIndexOf("["),O=h.value.slice(0,y),j=h.value.slice(y+2),P=DD[j];if(P){h.value=O+P,E.backtrack=!0,Q(),!s.output&&i.indexOf(h)===1&&(s.output=C);continue}}}(d==="["&&R()!==":"||d==="-"&&R()==="]")&&(d=`\\${d}`),d==="]"&&(h.value==="["||h.value==="[^")&&(d=`\\${d}`),u.posix===!0&&d==="!"&&h.value==="["&&(d="^"),h.value+=d,He({value:d});continue}if(E.quotes===1&&d!=='"'){d=z.escapeRegex(d),h.value+=d,He({value:d});continue}if(d==='"'){E.quotes=E.quotes===1?0:1,u.keepQuotes===!0&&w({type:"text",value:d});continue}if(d==="("){Pe("parens"),w({type:"paren",value:d});continue}if(d===")"){if(E.parens===0&&u.strictBrackets===!0)throw new SyntaxError(Ee("opening","("));const g=x[x.length-1];if(g&&E.parens===g.parens+1){Rr(x.pop());continue}w({type:"paren",value:d,output:E.parens?")":"\\)"}),ie("parens");continue}if(d==="["){if(u.nobracket===!0||!Z().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(Ee("closing","]"));d=`\\${d}`}else Pe("brackets");w({type:"bracket",value:d});continue}if(d==="]"){if(u.nobracket===!0||h&&h.type==="bracket"&&h.value.length===1){w({type:"text",value:d,output:`\\${d}`});continue}if(E.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(Ee("opening","["));w({type:"text",value:d,output:`\\${d}`});continue}ie("brackets");const g=h.value.slice(1);if(h.posix!==!0&&g[0]==="^"&&!g.includes("/")&&(d=`/${d}`),h.value+=d,He({value:d}),u.literalBrackets===!1||z.hasRegexChars(g))continue;const y=z.escapeRegex(h.value);if(E.output=E.output.slice(0,-h.value.length),u.literalBrackets===!0){E.output+=y,h.value=y;continue}h.value=`(${D}${y}|${h.value})`,E.output+=h.value;continue}if(d==="{"&&u.nobrace!==!0){Pe("braces");const g={type:"brace",value:d,output:"(",outputIndex:E.output.length,tokensIndex:E.tokens.length};$.push(g),w(g);continue}if(d==="}"){const g=$[$.length-1];if(u.nobrace===!0||!g){w({type:"text",value:d,output:d});continue}let y=")";if(g.dots===!0){const O=i.slice(),j=[];for(let P=O.length-1;P>=0&&(i.pop(),O[P].type!=="brace");P--)O[P].type!=="dots"&&j.unshift(O[P].value);y=lD(j,u),E.backtrack=!0}if(g.comma!==!0&&g.dots!==!0){const O=E.output.slice(0,g.outputIndex),j=E.tokens.slice(g.tokensIndex);g.value=g.output="\\{",d=y="\\}",E.output=O;for(const P of j)E.output+=P.output||P.value}w({type:"brace",value:d,output:y}),ie("braces"),$.pop();continue}if(d==="|"){x.length>0&&x[x.length-1].conditions++,w({type:"text",value:d});continue}if(d===","){let g=d;const y=$[$.length-1];y&&re[re.length-1]==="braces"&&(y.comma=!0,g="|"),w({type:"comma",value:d,output:g});continue}if(d==="/"){if(h.type==="dot"&&E.index===E.start+1){E.start=E.index+1,E.consumed="",E.output="",i.pop(),h=s;continue}w({type:"slash",value:d,output:p});continue}if(d==="."){if(E.braces>0&&h.type==="dot"){h.value==="."&&(h.output=f);const g=$[$.length-1];h.type="dots",h.output+=d,h.value+=d,g.dots=!0;continue}if(E.braces+E.parens===0&&h.type!=="bos"&&h.type!=="slash"){w({type:"text",value:d,output:f});continue}w({type:"dot",value:d,output:f});continue}if(d==="?"){if(!(h&&h.value==="(")&&u.noextglob!==!0&&R()==="("&&R(2)!=="?"){Le("qmark",d);continue}if(h&&h.type==="paren"){const y=R();let O=d;if(y==="<"&&!z.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(h.value==="("&&!/[!=<:]/.test(y)||y==="<"&&!/<([!=]|\w+>)/.test(Z()))&&(O=`\\${d}`),w({type:"text",value:d,output:O});continue}if(u.dot!==!0&&(h.type==="slash"||h.type==="bos")){w({type:"qmark",value:d,output:X});continue}w({type:"qmark",value:d,output:S});continue}if(d==="!"){if(u.noextglob!==!0&&R()==="("&&(R(2)!=="?"||!/[!=<:]/.test(R(3)))){Le("negate",d);continue}if(u.nonegate!==!0&&E.index===0){wr();continue}}if(d==="+"){if(u.noextglob!==!0&&R()==="("&&R(2)!=="?"){Le("plus",d);continue}if(h&&h.value==="("||u.regex===!1){w({type:"plus",value:d,output:l});continue}if(h&&(h.type==="bracket"||h.type==="paren"||h.type==="brace")||E.parens>0){w({type:"plus",value:d});continue}w({type:"plus",value:l});continue}if(d==="@"){if(u.noextglob!==!0&&R()==="("&&R(2)!=="?"){w({type:"at",extglob:!0,value:d,output:""});continue}w({type:"text",value:d});continue}if(d!=="*"){(d==="$"||d==="^")&&(d=`\\${d}`);const g=oD.exec(Z());g&&(d+=g[0],E.index+=g[0].length),w({type:"text",value:d});continue}if(h&&(h.type==="globstar"||h.star===!0)){h.type="star",h.star=!0,h.value+=d,h.output=k,E.backtrack=!0,E.globstar=!0,V(d);continue}let _=Z();if(u.noextglob!==!0&&/^\([^?]/.test(_)){Le("star",d);continue}if(h.type==="star"){if(u.noglobstar===!0){V(d);continue}const g=h.prev,y=g.prev,O=g.type==="slash"||g.type==="bos",j=y&&(y.type==="star"||y.type==="globstar");if(u.bash===!0&&(!O||_[0]&&_[0]!=="/")){w({type:"star",value:d,output:""});continue}const P=E.braces>0&&(g.type==="comma"||g.type==="brace"),ot=x.length&&(g.type==="pipe"||g.type==="paren");if(!O&&g.type!=="paren"&&!P&&!ot){w({type:"star",value:d,output:""});continue}for(;_.slice(0,3)==="/**";){const Ie=t[E.index+4];if(Ie&&Ie!=="/")break;_=_.slice(3),V("/**",3)}if(g.type==="bos"&&G()){h.type="globstar",h.value+=d,h.output=W(u),E.output=h.output,E.globstar=!0,V(d);continue}if(g.type==="slash"&&g.prev.type!=="bos"&&!j&&G()){E.output=E.output.slice(0,-(g.output+h.output).length),g.output=`(?:${g.output}`,h.type="globstar",h.output=W(u)+(u.strictSlashes?")":"|$)"),h.value+=d,E.globstar=!0,E.output+=g.output+h.output,V(d);continue}if(g.type==="slash"&&g.prev.type!=="bos"&&_[0]==="/"){const Ie=_[1]!==void 0?"|$":"";E.output=E.output.slice(0,-(g.output+h.output).length),g.output=`(?:${g.output}`,h.type="globstar",h.output=`${W(u)}${p}|${p}${Ie})`,h.value+=d,E.output+=g.output+h.output,E.globstar=!0,V(d+Q()),w({type:"slash",value:"/",output:""});continue}if(g.type==="bos"&&_[0]==="/"){h.type="globstar",h.value+=d,h.output=`(?:^|${p}|${W(u)}${p})`,E.output=h.output,E.globstar=!0,V(d+Q()),w({type:"slash",value:"/",output:""});continue}E.output=E.output.slice(0,-h.output.length),h.type="globstar",h.output=W(u),h.value+=d,E.output+=h.output,E.globstar=!0,V(d);continue}const T={type:"star",value:d,output:k};if(u.bash===!0){T.output=".*?",(h.type==="bos"||h.type==="slash")&&(T.output=m+T.output),w(T);continue}if(h&&(h.type==="bracket"||h.type==="paren")&&u.regex===!0){T.output=d,w(T);continue}(E.index===E.start||h.type==="slash"||h.type==="dot")&&(h.type==="dot"?(E.output+=B,h.output+=B):u.dot===!0?(E.output+=H,h.output+=H):(E.output+=m,h.output+=m),R()!=="*"&&(E.output+=C,h.output+=C)),w(T)}for(;E.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing","]"));E.output=z.escapeLast(E.output,"["),ie("brackets")}for(;E.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing",")"));E.output=z.escapeLast(E.output,"("),ie("parens")}for(;E.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(Ee("closing","}"));E.output=z.escapeLast(E.output,"{"),ie("braces")}if(u.strictSlashes!==!0&&(h.type==="star"||h.type==="bracket")&&w({type:"maybe_slash",value:"",output:`${p}?`}),E.backtrack===!0){E.output="";for(const _ of E.tokens)E.output+=_.output!=null?_.output:_.value,_.suffix&&(E.output+=_.suffix)}return E};Pt.fastpaths=(t,e)=>{const u={...e},n=typeof u.maxLength=="number"?Math.min(Ye,u.maxLength):Ye,r=t.length;if(r>n)throw new SyntaxError(`Input length: ${r}, exceeds maximum allowed length: ${n}`);t=Cn[t]||t;const s=z.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:D,ONE_CHAR:o,DOTS_SLASH:a,NO_DOT:c,NO_DOTS:f,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=ze.globChars(s),F=u.dot?f:c,A=u.dot?l:c,B=u.capture?"":"?:",H={negated:!1,prefix:""};let S=u.bash===!0?".*?":p;u.capture&&(S=`(${S})`);const X=m=>m.noglobstar===!0?S:`(${B}(?:(?!${C}${m.dot?a:i}).)*?)`,M=m=>{switch(m){case"*":return`${F}${o}${S}`;case".*":return`${i}${o}${S}`;case"*.*":return`${F}${S}${i}${o}${S}`;case"*/*":return`${F}${S}${D}${o}${A}${S}`;case"**":return F+X(u);case"**/*":return`(?:${F}${X(u)}${D})?${A}${o}${S}`;case"**/*.*":return`(?:${F}${X(u)}${D})?${A}${S}${i}${o}${S}`;case"**/.*":return`(?:${F}${X(u)}${D})?${i}${o}${S}`;default:{const b=/^(.*?)\.(\w+)$/.exec(m);if(!b)return;const k=M(b[1]);return k?k+i+b[2]:void 0}}},ne=z.removePrefix(t,H);let W=M(ne);return W&&u.strictSlashes!==!0&&(W+=`${D}?`),W};var cD=Pt;const fD=K.default,hD=iD,Lt=cD,It=Ue,dD=Ve,ED=t=>t&&typeof t=="object"&&!Array.isArray(t),N=(t,e,u=!1)=>{if(Array.isArray(t)){const c=t.map(l=>N(l,e,u));return l=>{for(const p of c){const C=p(l);if(C)return C}return!1}}const n=ED(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!n)throw new TypeError("Expected pattern to be a non-empty string");const r=e||{},s=It.isWindows(e),i=n?N.compileRe(t,e):N.makeRe(t,e,!1,!0),D=i.state;delete i.state;let o=()=>!1;if(r.ignore){const c={...e,ignore:null,onMatch:null,onResult:null};o=N(r.ignore,c,u)}const a=(c,f=!1)=>{const{isMatch:l,match:p,output:C}=N.test(c,i,e,{glob:t,posix:s}),F={glob:t,state:D,regex:i,posix:s,input:c,output:C,match:p,isMatch:l};return typeof r.onResult=="function"&&r.onResult(F),l===!1?(F.isMatch=!1,f?F:!1):o(c)?(typeof r.onIgnore=="function"&&r.onIgnore(F),F.isMatch=!1,f?F:!1):(typeof r.onMatch=="function"&&r.onMatch(F),f?F:!0)};return u&&(a.state=D),a};N.test=(t,e,u,{glob:n,posix:r}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const s=u||{},i=s.format||(r?It.toPosixSlashes:null);let D=t===n,o=D&&i?i(t):t;return D===!1&&(o=i?i(t):t,D=o===n),(D===!1||s.capture===!0)&&(s.matchBase===!0||s.basename===!0?D=N.matchBase(t,e,u,r):D=e.exec(o)),{isMatch:!!D,match:D,output:o}},N.matchBase=(t,e,u,n=It.isWindows(u))=>(e instanceof RegExp?e:N.makeRe(e,u)).test(fD.basename(t)),N.isMatch=(t,e,u)=>N(e,u)(t),N.parse=(t,e)=>Array.isArray(t)?t.map(u=>N.parse(u,e)):Lt(t,{...e,fastpaths:!1}),N.scan=(t,e)=>hD(t,e),N.compileRe=(t,e,u=!1,n=!1)=>{if(u===!0)return t.output;const r=e||{},s=r.contains?"":"^",i=r.contains?"":"$";let D=`${s}(?:${t.output})${i}`;t&&t.negated===!0&&(D=`^(?!${D}).*$`);const o=N.toRegex(D,e);return n===!0&&(o.state=t),o},N.makeRe=(t,e={},u=!1,n=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let r={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(r.output=Lt.fastpaths(t,e)),r.output||(r=Lt(t,e)),N.compileRe(r,e,u,n)},N.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},N.constants=dD;var pD=N,Fn=pD;const Re=De.default,{Readable:CD}=Ir.default,be=K.default,{promisify:qe}=me.default,kt=Fn,FD=qe(Re.readdir),gD=qe(Re.stat),gn=qe(Re.lstat),_D=qe(Re.realpath),mD="!",_n="READDIRP_RECURSIVE_ERROR",AD=new Set(["ENOENT","EPERM","EACCES","ELOOP",_n]),Mt="files",mn="directories",Xe="files_directories",Qe="all",An=[Mt,mn,Xe,Qe],yD=t=>AD.has(t.code),[yn,wD]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),RD=process.platform==="win32"&&(yn>10||yn===10&&wD>=5),wn=t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=kt(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const n of t){const r=n.trim();r.charAt(0)===mD?u.push(kt(r.slice(1))):e.push(kt(r))}return u.length>0?e.length>0?n=>e.some(r=>r(n.basename))&&!u.some(r=>r(n.basename)):n=>!u.some(r=>r(n.basename)):n=>e.some(r=>r(n.basename))}}};class Dt extends CD{static get defaultOptions(){return{root:".",fileFilter:e=>!0,directoryFilter:e=>!0,type:Mt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...Dt.defaultOptions,...e},{root:n,type:r}=u;this._fileFilter=wn(u.fileFilter),this._directoryFilter=wn(u.directoryFilter);const s=u.lstat?gn:gD;RD?this._stat=i=>s(i,{bigint:!0}):this._stat=s,this._maxDepth=u.depth,this._wantsDir=[mn,Xe,Qe].includes(r),this._wantsFile=[Mt,Xe,Qe].includes(r),this._wantsEverything=r===Qe,this._root=be.resolve(n),this._isDirent="Dirent"in Re&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(n,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:n,files:r=[]}=this.parent||{};if(r.length>0){const s=r.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(s)){if(this.destroyed)return;const D=await this._getEntryType(i);D==="directory"&&this._directoryFilter(i)?(n<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,n+1)),this._wantsDir&&(this.push(i),e--)):(D==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const s=this.parents.pop();if(!s){this.push(null);break}if(this.parent=await s,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let n;try{n=await FD(e,this._rdOptions)}catch(r){this._onError(r)}return{files:n,depth:u,path:e}}async _formatEntry(e,u){let n;try{const r=this._isDirent?e.name:e,s=be.resolve(be.join(u,r));n={path:be.relative(this._root,s),fullPath:s,basename:r},n[this._statsProp]=this._isDirent?e:await this._stat(s)}catch(r){this._onError(r)}return n}_onError(e){yD(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const n=e.fullPath;try{const r=await _D(n),s=await gn(r);if(s.isFile())return"file";if(s.isDirectory()){const i=r.length;if(n.startsWith(r)&&n.substr(i,1)===be.sep){const D=new Error(`Circular symlink detected: "${n}" points to "${r}"`);return D.code=_n,this._onError(D)}return"directory"}}catch(r){this._onError(r)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const pe=(t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=Xe),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!An.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${An.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new Dt(e)},bD=(t,e={})=>new Promise((u,n)=>{const r=[];pe(t,e).on("data",s=>r.push(s)).on("end",()=>u(r)).on("error",s=>n(s))});pe.promise=bD,pe.ReaddirpStream=Dt,pe.default=pe;var vD=pe,Wt={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var Rn=function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var n="";if(u>4&&t[3]==="\\"){var r=t[2];(r==="?"||r===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),n="//")}var s=t.split(/[/\\]+/);return e!==!1&&s[s.length-1]===""&&s.pop(),n+s.join("/")},BD=Wt.exports;Object.defineProperty(BD,"__esModule",{value:!0});const bn=Fn,SD=Rn,vn="!",$D={returnIndex:!1},TD=t=>Array.isArray(t)?t:[t],xD=(t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=bn(t,e);return n=>t===n||u(n)}return t instanceof RegExp?u=>t.test(u):u=>!1},Bn=(t,e,u,n)=>{const r=Array.isArray(u),s=r?u[0]:u;if(!r&&typeof s!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(s));const i=SD(s,!1);for(let o=0;o<e.length;o++){const a=e[o];if(a(i))return n?-1:!1}const D=r&&[i].concat(u.slice(1));for(let o=0;o<t.length;o++){const a=t[o];if(r?a(...D):a(i))return n?o:!0}return n?-1:!1},Gt=(t,e,u=$D)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const n=typeof u=="boolean"?{returnIndex:u}:u,r=n.returnIndex||!1,s=TD(t),i=s.filter(o=>typeof o=="string"&&o.charAt(0)===vn).map(o=>o.slice(1)).map(o=>bn(o,n)),D=s.filter(o=>typeof o!="string"||typeof o=="string"&&o.charAt(0)!==vn).map(o=>xD(o,n));return e==null?(o,a=!1)=>Bn(D,i,o,typeof a=="boolean"?a:!1):Bn(D,i,e,r)};Gt.default=Gt,Wt.exports=Gt;var OD=Wt.exports;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var ND=function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1};/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var HD=ND,Sn={"{":"}","(":")","[":"]"},PD=function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,n=-2,r=-2,s=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||n!==-1&&t[e]==="["&&t[e+1]!=="]"&&(n<e&&(n=t.indexOf("]",e)),n>e&&(i===-1||i>n||(i=t.indexOf("\\",e),i===-1||i>n)))||r!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(r=t.indexOf("}",e),r>e&&(i=t.indexOf("\\",e),i===-1||i>r))||s!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(s=t.indexOf(")",e),s>e&&(i=t.indexOf("\\",e),i===-1||i>s))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(s=t.indexOf(")",u),s>u&&(i=t.indexOf("\\",u),i===-1||i>s))))return!0;if(t[e]==="\\"){var D=t[e+1];e+=2;var o=Sn[D];if(o){var a=t.indexOf(o,e);a!==-1&&(e=a+1)}if(t[e]==="!")return!0}else e++}return!1},LD=function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var n=Sn[u];if(n){var r=t.indexOf(n,e);r!==-1&&(e=r+1)}if(t[e]==="!")return!0}else e++}return!1},$n=function(e,u){if(typeof e!="string"||e==="")return!1;if(HD(e))return!0;var n=PD;return u&&u.strict===!1&&(n=LD),n(e)},ID=$n,kD=K.default.posix.dirname,MD=Au.default.platform()==="win32",jt="/",WD=/\\/g,GD=/[\{\[].*[\}\]]$/,jD=/(^|[^\\])([\{\[]|\([^\)]+$)/,UD=/\\([\!\*\?\|\[\]\(\)\{\}])/g,KD=function(e,u){var n=Object.assign({flipBackslashes:!0},u);n.flipBackslashes&&MD&&e.indexOf(jt)<0&&(e=e.replace(WD,jt)),GD.test(e)&&(e+=jt),e+="a";do e=kD(e);while(ID(e)||jD.test(e));return e.replace(UD,"$1")},Ze={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(n=>n.type===u),t.exceedsLimit=(e,u,n=1,r)=>r===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(n)>=r,t.escapeNode=(e,u=0,n)=>{let r=e.nodes[u];r&&(n&&r.type===n||r.type==="open"||r.type==="close")&&r.escaped!==!0&&(r.value="\\"+r.value,r.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"||e.commas>>0+e.ranges>>0?!1:(e.invalid=!0,!0),t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:!(e.commas>>0+e.ranges>>0)||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,n)=>(n.type==="text"&&u.push(n.value),n.type==="range"&&(n.type="text"),u),[]),t.flatten=(...e)=>{const u=[],n=r=>{for(let s=0;s<r.length;s++){let i=r[s];Array.isArray(i)?n(i):i!==void 0&&u.push(i)}return u};return n(e),u}})(Ze);const Tn=Ze;var Ut=(t,e={})=>{let u=(n,r={})=>{let s=e.escapeInvalid&&Tn.isInvalidBrace(r),i=n.invalid===!0&&e.escapeInvalid===!0,D="";if(n.value)return(s||i)&&Tn.isOpenOrClose(n)?"\\"+n.value:n.value;if(n.value)return n.value;if(n.nodes)for(let o of n.nodes)D+=u(o);return D};return u(t)};/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var VD=function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1};/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const xn=VD,ae=(t,e,u)=>{if(xn(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(xn(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let n={relaxZeros:!0,...u};typeof n.strictZeros=="boolean"&&(n.relaxZeros=n.strictZeros===!1);let r=String(n.relaxZeros),s=String(n.shorthand),i=String(n.capture),D=String(n.wrap),o=t+":"+e+"="+r+s+i+D;if(ae.cache.hasOwnProperty(o))return ae.cache[o].result;let a=Math.min(t,e),c=Math.max(t,e);if(Math.abs(a-c)===1){let F=t+"|"+e;return n.capture?`(${F})`:n.wrap===!1?F:`(?:${F})`}let f=In(t)||In(e),l={min:t,max:e,a,b:c},p=[],C=[];if(f&&(l.isPadded=f,l.maxLen=String(l.max).length),a<0){let F=c<0?Math.abs(c):1;C=On(F,Math.abs(a),l,n),a=l.a=0}return c>=0&&(p=On(a,c,l,n)),l.negatives=C,l.positives=p,l.result=zD(C,p),n.capture===!0?l.result=`(${l.result})`:n.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),ae.cache[o]=l,l.result};function zD(t,e,u){let n=Kt(t,e,"-",!1)||[],r=Kt(e,t,"",!1)||[],s=Kt(t,e,"-?",!0)||[];return n.concat(s).concat(r).join("|")}function YD(t,e){let u=1,n=1,r=Hn(t,u),s=new Set([e]);for(;t<=r&&r<=e;)s.add(r),u+=1,r=Hn(t,u);for(r=Pn(e+1,n)-1;t<r&&r<=e;)s.add(r),n+=1,r=Pn(e+1,n)-1;return s=[...s],s.sort(QD),s}function qD(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let n=XD(t,e),r=n.length,s="",i=0;for(let D=0;D<r;D++){let[o,a]=n[D];o===a?s+=o:o!=="0"||a!=="9"?s+=ZD(o,a):i++}return i&&(s+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:s,count:[i],digits:r}}function On(t,e,u,n){let r=YD(t,e),s=[],i=t,D;for(let o=0;o<r.length;o++){let a=r[o],c=qD(String(i),String(a),n),f="";if(!u.isPadded&&D&&D.pattern===c.pattern){D.count.length>1&&D.count.pop(),D.count.push(c.count[0]),D.string=D.pattern+Ln(D.count),i=a+1;continue}u.isPadded&&(f=JD(a,u,n)),c.string=f+c.pattern+Ln(c.count),s.push(c),i=a+1,D=c}return s}function Kt(t,e,u,n,r){let s=[];for(let i of t){let{string:D}=i;!n&&!Nn(e,"string",D)&&s.push(u+D),n&&Nn(e,"string",D)&&s.push(u+D)}return s}function XD(t,e){let u=[];for(let n=0;n<t.length;n++)u.push([t[n],e[n]]);return u}function QD(t,e){return t>e?1:e>t?-1:0}function Nn(t,e,u){return t.some(n=>n[e]===u)}function Hn(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}function Pn(t,e){return t-t%Math.pow(10,e)}function Ln(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}function ZD(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}function In(t){return/^-?(0+)\d/.test(t)}function JD(t,e,u){if(!e.isPadded)return t;let n=Math.abs(e.maxLen-String(t).length),r=u.relaxZeros!==!1;switch(n){case 0:return"";case 1:return r?"0?":"0";case 2:return r?"0{0,2}":"00";default:return r?`0{0,${n}}`:`0{${n}}`}}ae.cache={},ae.clearCache=()=>ae.cache={};var eo=ae;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const to=me.default,kn=eo,Mn=t=>t!==null&&typeof t=="object"&&!Array.isArray(t),uo=t=>e=>t===!0?Number(e):String(e),Vt=t=>typeof t=="number"||typeof t=="string"&&t!=="",ve=t=>Number.isInteger(+t),zt=t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},no=(t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,ro=(t,e,u)=>{if(e>0){let n=t[0]==="-"?"-":"";n&&(t=t.slice(1)),t=n+t.padStart(n?e-1:e,"0")}return u===!1?String(t):t},Wn=(t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},so=(t,e)=>{t.negatives.sort((i,D)=>i<D?-1:i>D?1:0),t.positives.sort((i,D)=>i<D?-1:i>D?1:0);let u=e.capture?"":"?:",n="",r="",s;return t.positives.length&&(n=t.positives.join("|")),t.negatives.length&&(r=`-(${u}${t.negatives.join("|")})`),n&&r?s=`${n}|${r}`:s=n||r,e.wrap?`(${u}${s})`:s},Gn=(t,e,u,n)=>{if(u)return kn(t,e,{wrap:!1,...n});let r=String.fromCharCode(t);if(t===e)return r;let s=String.fromCharCode(e);return`[${r}-${s}]`},jn=(t,e,u)=>{if(Array.isArray(t)){let n=u.wrap===!0,r=u.capture?"":"?:";return n?`(${r}${t.join("|")})`:t.join("|")}return kn(t,e,u)},Un=(...t)=>new RangeError("Invalid range arguments: "+to.inspect(...t)),Kn=(t,e,u)=>{if(u.strictRanges===!0)throw Un([t,e]);return[]},io=(t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},Do=(t,e,u=1,n={})=>{let r=Number(t),s=Number(e);if(!Number.isInteger(r)||!Number.isInteger(s)){if(n.strictRanges===!0)throw Un([t,e]);return[]}r===0&&(r=0),s===0&&(s=0);let i=r>s,D=String(t),o=String(e),a=String(u);u=Math.max(Math.abs(u),1);let c=zt(D)||zt(o)||zt(a),f=c?Math.max(D.length,o.length,a.length):0,l=c===!1&&no(t,e,n)===!1,p=n.transform||uo(l);if(n.toRegex&&u===1)return Gn(Wn(t,f),Wn(e,f),!0,n);let C={negatives:[],positives:[]},F=H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),A=[],B=0;for(;i?r>=s:r<=s;)n.toRegex===!0&&u>1?F(r):A.push(ro(p(r,B),f,l)),r=i?r-u:r+u,B++;return n.toRegex===!0?u>1?so(C,n):jn(A,null,{wrap:!1,...n}):A},oo=(t,e,u=1,n={})=>{if(!ve(t)&&t.length>1||!ve(e)&&e.length>1)return Kn(t,e,n);let r=n.transform||(l=>String.fromCharCode(l)),s=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),D=s>i,o=Math.min(s,i),a=Math.max(s,i);if(n.toRegex&&u===1)return Gn(o,a,!1,n);let c=[],f=0;for(;D?s>=i:s<=i;)c.push(r(s,f)),s=D?s-u:s+u,f++;return n.toRegex===!0?jn(c,null,{wrap:!1,options:n}):c},Je=(t,e,u,n={})=>{if(e==null&&Vt(t))return[t];if(!Vt(t)||!Vt(e))return Kn(t,e,n);if(typeof u=="function")return Je(t,e,1,{transform:u});if(Mn(u))return Je(t,e,0,u);let r={...n};return r.capture===!0&&(r.wrap=!0),u=u||r.step||1,ve(u)?ve(t)&&ve(e)?Do(t,e,u,r):oo(t,e,Math.max(Math.abs(u),1),r):u!=null&&!Mn(u)?io(u,r):Je(t,e,1,u)};var Vn=Je;const ao=Vn,zn=Ze,lo=(t,e={})=>{let u=(n,r={})=>{let s=zn.isInvalidBrace(r),i=n.invalid===!0&&e.escapeInvalid===!0,D=s===!0||i===!0,o=e.escapeInvalid===!0?"\\":"",a="";if(n.isOpen===!0||n.isClose===!0)return o+n.value;if(n.type==="open")return D?o+n.value:"(";if(n.type==="close")return D?o+n.value:")";if(n.type==="comma")return n.prev.type==="comma"?"":D?n.value:"|";if(n.value)return n.value;if(n.nodes&&n.ranges>0){let c=zn.reduce(n.nodes),f=ao(...c,{...e,wrap:!1,toRegex:!0});if(f.length!==0)return c.length>1&&f.length>1?`(${f})`:f}if(n.nodes)for(let c of n.nodes)a+=u(c,n);return a};return u(t)};var co=lo;const fo=Vn,Yn=Ut,Ce=Ze,le=(t="",e="",u=!1)=>{let n=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?Ce.flatten(e).map(r=>`{${r}}`):e;for(let r of t)if(Array.isArray(r))for(let s of r)n.push(le(s,e,u));else for(let s of e)u===!0&&typeof s=="string"&&(s=`{${s}}`),n.push(Array.isArray(s)?le(r,s,u):r+s);return Ce.flatten(n)},ho=(t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,n=(r,s={})=>{r.queue=[];let i=s,D=s.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,D=i.queue;if(r.invalid||r.dollar){D.push(le(D.pop(),Yn(r,e)));return}if(r.type==="brace"&&r.invalid!==!0&&r.nodes.length===2){D.push(le(D.pop(),["{}"]));return}if(r.nodes&&r.ranges>0){let f=Ce.reduce(r.nodes);if(Ce.exceedsLimit(...f,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=fo(...f,e);l.length===0&&(l=Yn(r,e)),D.push(le(D.pop(),l)),r.nodes=[];return}let o=Ce.encloseBrace(r),a=r.queue,c=r;for(;c.type!=="brace"&&c.type!=="root"&&c.parent;)c=c.parent,a=c.queue;for(let f=0;f<r.nodes.length;f++){let l=r.nodes[f];if(l.type==="comma"&&r.type==="brace"){f===1&&a.push(""),a.push("");continue}if(l.type==="close"){D.push(le(D.pop(),a,o));continue}if(l.value&&l.type!=="open"){a.push(le(a.pop(),l.value));continue}l.nodes&&n(l,r)}return a};return Ce.flatten(n(t))};var Eo=ho,po={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const Co=Ut,{MAX_LENGTH:qn,CHAR_BACKSLASH:Yt,CHAR_BACKTICK:Fo,CHAR_COMMA:go,CHAR_DOT:_o,CHAR_LEFT_PARENTHESES:mo,CHAR_RIGHT_PARENTHESES:Ao,CHAR_LEFT_CURLY_BRACE:yo,CHAR_RIGHT_CURLY_BRACE:wo,CHAR_LEFT_SQUARE_BRACKET:Xn,CHAR_RIGHT_SQUARE_BRACKET:Qn,CHAR_DOUBLE_QUOTE:Ro,CHAR_SINGLE_QUOTE:bo,CHAR_NO_BREAK_SPACE:vo,CHAR_ZERO_WIDTH_NOBREAK_SPACE:Bo}=po,So=(t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},n=typeof u.maxLength=="number"?Math.min(qn,u.maxLength):qn;if(t.length>n)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${n})`);let r={type:"root",input:t,nodes:[]},s=[r],i=r,D=r,o=0,a=t.length,c=0,f=0,l;const p=()=>t[c++],C=F=>{if(F.type==="text"&&D.type==="dot"&&(D.type="text"),D&&D.type==="text"&&F.type==="text"){D.value+=F.value;return}return i.nodes.push(F),F.parent=i,F.prev=D,D=F,F};for(C({type:"bos"});c<a;)if(i=s[s.length-1],l=p(),!(l===Bo||l===vo)){if(l===Yt){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===Qn){C({type:"text",value:"\\"+l});continue}if(l===Xn){o++;let F;for(;c<a&&(F=p());){if(l+=F,F===Xn){o++;continue}if(F===Yt){l+=p();continue}if(F===Qn&&(o--,o===0))break}C({type:"text",value:l});continue}if(l===mo){i=C({type:"paren",nodes:[]}),s.push(i),C({type:"text",value:l});continue}if(l===Ao){if(i.type!=="paren"){C({type:"text",value:l});continue}i=s.pop(),C({type:"text",value:l}),i=s[s.length-1];continue}if(l===Ro||l===bo||l===Fo){let F=l,A;for(e.keepQuotes!==!0&&(l="");c<a&&(A=p());){if(A===Yt){l+=A+p();continue}if(A===F){e.keepQuotes===!0&&(l+=A);break}l+=A}C({type:"text",value:l});continue}if(l===yo){f++;let A={type:"brace",open:!0,close:!1,dollar:D.value&&D.value.slice(-1)==="$"||i.dollar===!0,depth:f,commas:0,ranges:0,nodes:[]};i=C(A),s.push(i),C({type:"open",value:l});continue}if(l===wo){if(i.type!=="brace"){C({type:"text",value:l});continue}let F="close";i=s.pop(),i.close=!0,C({type:F,value:l}),f--,i=s[s.length-1];continue}if(l===go&&f>0){if(i.ranges>0){i.ranges=0;let F=i.nodes.shift();i.nodes=[F,{type:"text",value:Co(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===_o&&f>0&&i.commas===0){let F=i.nodes;if(f===0||F.length===0){C({type:"text",value:l});continue}if(D.type==="dot"){if(i.range=[],D.value+=l,D.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,D.type="text";continue}i.ranges++,i.args=[];continue}if(D.type==="range"){F.pop();let A=F[F.length-1];A.value+=D.value+l,D=A,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=s.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let F=s[s.length-1],A=F.nodes.indexOf(i);F.nodes.splice(A,1,...i.nodes)}while(s.length>0);return C({type:"eos"}),r};var $o=So;const Zn=Ut,To=co,xo=Eo,Oo=$o,Y=(t,e={})=>{let u=[];if(Array.isArray(t))for(let n of t){let r=Y.create(n,e);Array.isArray(r)?u.push(...r):u.push(r)}else u=[].concat(Y.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u};Y.parse=(t,e={})=>Oo(t,e),Y.stringify=(t,e={})=>Zn(typeof t=="string"?Y.parse(t,e):t,e),Y.compile=(t,e={})=>(typeof t=="string"&&(t=Y.parse(t,e)),To(t,e)),Y.expand=(t,e={})=>{typeof t=="string"&&(t=Y.parse(t,e));let u=xo(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},Y.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?Y.compile(t,e):Y.expand(t,e);var No=Y,Ho=["3dm","3ds","3g2","3gp","7z","a","aac","adp","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"],Po=Ho;const Lo=K.default,Io=Po,ko=new Set(Io);var Mo=t=>ko.has(Lo.extname(t).slice(1).toLowerCase()),et={};(function(t){const{sep:e}=K.default,{platform:u}=process,n=Au.default;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=r=>r,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=n.type()==="OS400"})(et);const ue=De.default,L=K.default,{promisify:Be}=me.default,Wo=Mo,{isWindows:Go,isLinux:jo,EMPTY_FN:Uo,EMPTY_STR:Ko,KEY_LISTENERS:Fe,KEY_ERR:qt,KEY_RAW:Se,HANDLER_KEYS:Vo,EV_CHANGE:tt,EV_ADD:ut,EV_ADD_DIR:zo,EV_ERROR:Jn,STR_DATA:Yo,STR_END:qo,BRACE_START:Xo,STAR:Qo}=et,Zo="watch",Jo=Be(ue.open),er=Be(ue.stat),ea=Be(ue.lstat),ta=Be(ue.close),Xt=Be(ue.realpath),ua={lstat:ea,stat:er},Qt=(t,e)=>{t instanceof Set?t.forEach(e):e(t)},$e=(t,e,u)=>{let n=t[e];n instanceof Set||(t[e]=n=new Set([n])),n.add(u)},na=t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},Te=(t,e,u)=>{const n=t[e];n instanceof Set?n.delete(u):n===u&&delete t[e]},tr=t=>t instanceof Set?t.size===0:!t,nt=new Map;function ur(t,e,u,n,r){const s=(i,D)=>{u(t),r(i,D,{watchedPath:t}),D&&t!==D&&rt(L.resolve(t,D),Fe,L.join(t,D))};try{return ue.watch(t,e,s)}catch(i){n(i)}}const rt=(t,e,u,n,r)=>{const s=nt.get(t);s&&Qt(s[e],i=>{i(u,n,r)})},ra=(t,e,u,n)=>{const{listener:r,errHandler:s,rawEmitter:i}=n;let D=nt.get(e),o;if(!u.persistent)return o=ur(t,u,r,s,i),o.close.bind(o);if(D)$e(D,Fe,r),$e(D,qt,s),$e(D,Se,i);else{if(o=ur(t,u,rt.bind(null,e,Fe),s,rt.bind(null,e,Se)),!o)return;o.on(Jn,async a=>{const c=rt.bind(null,e,qt);if(D.watcherUnusable=!0,Go&&a.code==="EPERM")try{const f=await Jo(t,"r");await ta(f),c(a)}catch{}else c(a)}),D={listeners:r,errHandlers:s,rawEmitters:i,watcher:o},nt.set(e,D)}return()=>{Te(D,Fe,r),Te(D,qt,s),Te(D,Se,i),tr(D.listeners)&&(D.watcher.close(),nt.delete(e),Vo.forEach(na(D)),D.watcher=void 0,Object.freeze(D))}},Zt=new Map,sa=(t,e,u,n)=>{const{listener:r,rawEmitter:s}=n;let i=Zt.get(e);const D=i&&i.options;return D&&(D.persistent<u.persistent||D.interval>u.interval)&&(i.listeners,i.rawEmitters,ue.unwatchFile(e),i=void 0),i?($e(i,Fe,r),$e(i,Se,s)):(i={listeners:r,rawEmitters:s,options:u,watcher:ue.watchFile(e,u,(o,a)=>{Qt(i.rawEmitters,f=>{f(tt,e,{curr:o,prev:a})});const c=o.mtimeMs;(o.size!==a.size||c>a.mtimeMs||c===0)&&Qt(i.listeners,f=>f(t,o))})},Zt.set(e,i)),()=>{Te(i,Fe,r),Te(i,Se,s),tr(i.listeners)&&(Zt.delete(e),ue.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}};class ia{constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const n=this.fsw.options,r=L.dirname(e),s=L.basename(e);this.fsw._getWatchedDir(r).add(s);const D=L.resolve(e),o={persistent:n.persistent};u||(u=Uo);let a;return n.usePolling?(o.interval=n.enableBinaryInterval&&Wo(s)?n.binaryInterval:n.interval,a=sa(e,D,o,{listener:u,rawEmitter:this.fsw._emitRaw})):a=ra(e,D,o,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),a}_handleFile(e,u,n){if(this.fsw.closed)return;const r=L.dirname(e),s=L.basename(e),i=this.fsw._getWatchedDir(r);let D=u;if(i.has(s))return;const o=async(c,f)=>{if(this.fsw._throttle(Zo,e,5)){if(!f||f.mtimeMs===0)try{const l=await er(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==D.mtimeMs)&&this.fsw._emit(tt,e,l),jo&&D.ino!==l.ino?(this.fsw._closeFile(c),D=l,this.fsw._addPathCloser(c,this._watchWithNodeFs(e,o))):D=l}catch{this.fsw._remove(r,s)}else if(i.has(s)){const l=f.atimeMs,p=f.mtimeMs;(!l||l<=p||p!==D.mtimeMs)&&this.fsw._emit(tt,e,f),D=f}}},a=this._watchWithNodeFs(e,o);if(!(n&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(ut,e,0))return;this.fsw._emit(ut,e,u)}return a}async _handleSymlink(e,u,n,r){if(this.fsw.closed)return;const s=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let D;try{D=await Xt(n)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(r)?this.fsw._symlinkPaths.get(s)!==D&&(this.fsw._symlinkPaths.set(s,D),this.fsw._emit(tt,n,e.stats)):(i.add(r),this.fsw._symlinkPaths.set(s,D),this.fsw._emit(ut,n,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(s))return!0;this.fsw._symlinkPaths.set(s,!0)}_handleRead(e,u,n,r,s,i,D){if(e=L.join(e,Ko),!n.hasGlob&&(D=this.fsw._throttle("readdir",e,1e3),!D))return;const o=this.fsw._getWatchedDir(n.path),a=new Set;let c=this.fsw._readdirp(e,{fileFilter:f=>n.filterPath(f),directoryFilter:f=>n.filterDir(f),depth:0}).on(Yo,async f=>{if(this.fsw.closed){c=void 0;return}const l=f.path;let p=L.join(e,l);if(a.add(l),!(f.stats.isSymbolicLink()&&await this._handleSymlink(f,e,p,l))){if(this.fsw.closed){c=void 0;return}(l===r||!r&&!o.has(l))&&(this.fsw._incrReadyCount(),p=L.join(s,L.relative(s,p)),this._addToNodeFs(p,u,n,i+1))}}).on(Jn,this._boundHandleError);return new Promise(f=>c.once(qo,()=>{if(this.fsw.closed){c=void 0;return}const l=D?D.clear():!1;f(),o.getChildren().filter(p=>p!==e&&!a.has(p)&&(!n.hasGlob||n.filterPath({fullPath:L.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),c=void 0,l&&this._handleRead(e,!1,n,r,s,i,D)}))}async _handleDir(e,u,n,r,s,i,D){const o=this.fsw._getWatchedDir(L.dirname(e)),a=o.has(L.basename(e));!(n&&this.fsw.options.ignoreInitial)&&!s&&!a&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(zo,e,u),o.add(L.basename(e)),this.fsw._getWatchedDir(e);let c,f;const l=this.fsw.options.depth;if((l==null||r<=l)&&!this.fsw._symlinkPaths.has(D)){if(!s&&(await this._handleRead(e,n,i,s,e,r,c),this.fsw.closed))return;f=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,s,e,r,c)})}return f}async _addToNodeFs(e,u,n,r,s){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const D=this.fsw._getWatchHelpers(e,r);!D.hasGlob&&n&&(D.hasGlob=n.hasGlob,D.globFilter=n.globFilter,D.filterPath=o=>n.filterPath(o),D.filterDir=o=>n.filterDir(o));try{const o=await ua[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))return i(),!1;const a=this.fsw.options.followSymlinks&&!e.includes(Qo)&&!e.includes(Xo);let c;if(o.isDirectory()){const f=L.resolve(e),l=a?await Xt(e):e;if(this.fsw.closed||(c=await this._handleDir(D.watchPath,o,u,r,s,D,l),this.fsw.closed))return;f!==l&&l!==void 0&&this.fsw._symlinkPaths.set(f,l)}else if(o.isSymbolicLink()){const f=a?await Xt(e):e;if(this.fsw.closed)return;const l=L.dirname(D.watchPath);if(this.fsw._getWatchedDir(l).add(D.watchPath),this.fsw._emit(ut,D.watchPath,o),c=await this._handleDir(l,o,u,r,e,D,f),this.fsw.closed)return;f!==void 0&&this.fsw._symlinkPaths.set(L.resolve(e),f)}else c=this._handleFile(D.watchPath,o,u);return i(),this.fsw._addPathCloser(e,c),!1}catch(o){if(this.fsw._handleError(o))return i(),e}}}var Da=ia,Jt={exports:{}};const eu=De.default,I=K.default,{promisify:tu}=me.default;let ge;try{ge=at.require("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(ge){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(ge=void 0)}}const{EV_ADD:uu,EV_CHANGE:oa,EV_ADD_DIR:nr,EV_UNLINK:st,EV_ERROR:aa,STR_DATA:la,STR_END:ca,FSEVENT_CREATED:fa,FSEVENT_MODIFIED:ha,FSEVENT_DELETED:da,FSEVENT_MOVED:Ea,FSEVENT_UNKNOWN:pa,FSEVENT_TYPE_FILE:Ca,FSEVENT_TYPE_DIRECTORY:xe,FSEVENT_TYPE_SYMLINK:rr,ROOT_GLOBSTAR:sr,DIR_SUFFIX:Fa,DOT_SLASH:ir,FUNCTION_TYPE:nu,EMPTY_FN:ga,IDENTITY_FN:_a}=et,ma=t=>isNaN(t)?{}:{depth:t},ru=tu(eu.stat),Aa=tu(eu.lstat),Dr=tu(eu.realpath),ya={stat:ru,lstat:Aa},ce=new Map,wa=10,Ra=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),ba=(t,e)=>({stop:ge.watch(t,e)});function va(t,e,u,n){let r=I.extname(e)?I.dirname(e):e;const s=I.dirname(r);let i=ce.get(r);Ba(s)&&(r=s);const D=I.resolve(t),o=D!==e,a=(f,l,p)=>{o&&(f=f.replace(e,D)),(f===D||!f.indexOf(D+I.sep))&&u(f,l,p)};let c=!1;for(const f of ce.keys())if(e.indexOf(I.resolve(f)+I.sep)===0){r=f,i=ce.get(r),c=!0;break}return i||c?i.listeners.add(a):(i={listeners:new Set([a]),rawEmitter:n,watcher:ba(r,(f,l)=>{if(!i.listeners.size)return;const p=ge.getInfo(f,l);i.listeners.forEach(C=>{C(f,l,p)}),i.rawEmitter(p.event,f,p)})},ce.set(r,i)),()=>{const f=i.listeners;if(f.delete(a),!f.size&&(ce.delete(r),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}const Ba=t=>{let e=0;for(const u of ce.keys())if(u.indexOf(t)===0&&(e++,e>=wa))return!0;return!1},Sa=()=>ge&&ce.size<128,su=(t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=I.dirname(t))!==e;)u++;return u},or=(t,e)=>t.type===xe&&e.isDirectory()||t.type===rr&&e.isSymbolicLink()||t.type===Ca&&e.isFile();class $a{constructor(e){this.fsw=e}checkIgnored(e,u){const n=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return n.add(e),u&&u.isDirectory()&&n.add(e+sr),!0;n.delete(e),n.delete(e+sr)}addOrChange(e,u,n,r,s,i,D,o){const a=s.has(i)?oa:uu;this.handleEvent(a,e,u,n,r,s,i,D,o)}async checkExists(e,u,n,r,s,i,D,o){try{const a=await ru(e);if(this.fsw.closed)return;or(D,a)?this.addOrChange(e,u,n,r,s,i,D,o):this.handleEvent(st,e,u,n,r,s,i,D,o)}catch(a){a.code==="EACCES"?this.addOrChange(e,u,n,r,s,i,D,o):this.handleEvent(st,e,u,n,r,s,i,D,o)}}handleEvent(e,u,n,r,s,i,D,o,a){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===st){const c=o.type===xe;(c||i.has(D))&&this.fsw._remove(s,D,c)}else{if(e===uu){if(o.type===xe&&this.fsw._getWatchedDir(u),o.type===rr&&a.followSymlinks){const f=a.depth===void 0?void 0:su(n,r)+1;return this._addToFsEvents(u,!1,!0,f)}this.fsw._getWatchedDir(s).add(D)}const c=o.type===xe?e+Fa:e;this.fsw._emit(c,u),c===nr&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,n,r){if(this.fsw.closed||this.fsw._isIgnored(e))return;const s=this.fsw.options,D=va(e,u,async(o,a,c)=>{if(this.fsw.closed||s.depth!==void 0&&su(o,u)>s.depth)return;const f=n(I.join(e,I.relative(e,o)));if(r&&!r(f))return;const l=I.dirname(f),p=I.basename(f),C=this.fsw._getWatchedDir(c.type===xe?f:l);if(Ra.has(a)||c.event===pa)if(typeof s.ignored===nu){let F;try{F=await ru(f)}catch{}if(this.fsw.closed||this.checkIgnored(f,F))return;or(c,F)?this.addOrChange(f,o,u,l,C,p,c,s):this.handleEvent(st,f,o,u,l,C,p,c,s)}else this.checkExists(f,o,u,l,C,p,c,s);else switch(c.event){case fa:case ha:return this.addOrChange(f,o,u,l,C,p,c,s);case da:case Ea:return this.checkExists(f,o,u,l,C,p,c,s)}},this.fsw._emitRaw);return this.fsw._emitReady(),D}async _handleFsEventsSymlink(e,u,n,r){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const s=await Dr(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(s))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(s||e,i=>{let D=e;return s&&s!==ir?D=i.replace(s,e):i!==ir&&(D=I.join(e,i)),n(D)},!1,r)}catch(s){if(this.fsw._handleError(s))return this.fsw._emitReady()}}}emitAdd(e,u,n,r,s){const i=n(e),D=u.isDirectory(),o=this.fsw._getWatchedDir(I.dirname(i)),a=I.basename(i);D&&this.fsw._getWatchedDir(i),!o.has(a)&&(o.add(a),(!r.ignoreInitial||s===!0)&&this.fsw._emit(D?nr:uu,i,u))}initWatch(e,u,n,r){if(this.fsw.closed)return;const s=this._watchWithFsEvents(n.watchPath,I.resolve(e||n.watchPath),r,n.globFilter);this.fsw._addPathCloser(u,s)}async _addToFsEvents(e,u,n,r){if(this.fsw.closed)return;const s=this.fsw.options,i=typeof u===nu?u:_a,D=this.fsw._getWatchHelpers(e);try{const o=await ya[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))throw null;if(o.isDirectory()){if(D.globFilter||this.emitAdd(i(e),o,i,s,n),r&&r>s.depth)return;this.fsw._readdirp(D.watchPath,{fileFilter:a=>D.filterPath(a),directoryFilter:a=>D.filterDir(a),...ma(s.depth-(r||0))}).on(la,a=>{if(this.fsw.closed||a.stats.isDirectory()&&!D.filterPath(a))return;const c=I.join(D.watchPath,a.path),{fullPath:f}=a;if(D.followSymlinks&&a.stats.isSymbolicLink()){const l=s.depth===void 0?void 0:su(c,I.resolve(D.watchPath))+1;this._handleFsEventsSymlink(c,f,i,l)}else this.emitAdd(c,a.stats,i,s,n)}).on(aa,ga).on(ca,()=>{this.fsw._emitReady()})}else this.emitAdd(D.watchPath,o,i,s,n),this.fsw._emitReady()}catch(o){(!o||this.fsw._handleError(o))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(s.persistent&&n!==!0)if(typeof u===nu)this.initWatch(void 0,e,D,i);else{let o;try{o=await Dr(D.watchPath)}catch{}this.initWatch(o,e,D,i)}}}Jt.exports=$a,Jt.exports.canUse=Sa;var Ta=Jt.exports;const{EventEmitter:xa}=Lr.default,iu=De.default,v=K.default,{promisify:ar}=me.default,Oa=vD,Du=OD.default,Na=KD,ou=$n,Ha=No,Pa=Rn,La=Da,lr=Ta,{EV_ALL:au,EV_READY:Ia,EV_ADD:it,EV_CHANGE:Oe,EV_UNLINK:cr,EV_ADD_DIR:ka,EV_UNLINK_DIR:Ma,EV_RAW:Wa,EV_ERROR:lu,STR_CLOSE:Ga,STR_END:ja,BACK_SLASH_RE:Ua,DOUBLE_SLASH_RE:fr,SLASH_OR_BACK_SLASH_RE:Ka,DOT_RE:Va,REPLACER_RE:za,SLASH:cu,SLASH_SLASH:Ya,BRACE_START:qa,BANG:fu,ONE_DOT:hr,TWO_DOTS:Xa,GLOBSTAR:Qa,SLASH_GLOBSTAR:hu,ANYMATCH_OPTS:du,STRING_TYPE:Eu,FUNCTION_TYPE:Za,EMPTY_STR:pu,EMPTY_FN:Ja,isWindows:el,isMacos:tl,isIBMi:ul}=et,nl=ar(iu.stat),rl=ar(iu.readdir),Cu=(t=[])=>Array.isArray(t)?t:[t],dr=(t,e=[])=>(t.forEach(u=>{Array.isArray(u)?dr(u,e):e.push(u)}),e),Er=t=>{const e=dr(Cu(t));if(!e.every(u=>typeof u===Eu))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(Cr)},pr=t=>{let e=t.replace(Ua,cu),u=!1;for(e.startsWith(Ya)&&(u=!0);e.match(fr);)e=e.replace(fr,cu);return u&&(e=cu+e),e},Cr=t=>pr(v.normalize(pr(t))),Fr=(t=pu)=>e=>typeof e!==Eu?e:Cr(v.isAbsolute(e)?e:v.join(t,e)),sl=(t,e)=>v.isAbsolute(t)?t:t.startsWith(fu)?fu+v.join(e,t.slice(1)):v.join(e,t),q=(t,e)=>t[e]===void 0;class il{constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;u&&e!==hr&&e!==Xa&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const n=this.path;try{await rl(n)}catch{this._removeWatcher&&this._removeWatcher(v.dirname(n),v.basename(n))}}has(e){const{items:u}=this;if(u)return u.has(e)}getChildren(){const{items:e}=this;if(e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const Dl="stat",ol="lstat";class al{constructor(e,u,n,r){this.fsw=r,this.path=e=e.replace(za,pu),this.watchPath=u,this.fullWatchPath=v.resolve(u),this.hasGlob=u!==e,e===pu&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&n?void 0:!1,this.globFilter=this.hasGlob?Du(e,void 0,du):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(s=>{s.length>1&&s.pop()}),this.followSymlinks=n,this.statMethod=n?Dl:ol}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return v.join(this.watchPath,v.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const n=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===Za?this.globFilter(n):!0)&&this.fsw._isntIgnored(n,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(qa)?Ha.expand(e):[e]).forEach(r=>{u.push(v.relative(this.watchPath,r).split(Ka))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let n=!1;this.unmatchedGlob=!this.dirParts.some(r=>r.every((s,i)=>(s===Qa&&(n=!0),n||!u[0][i]||Du(s,u[0][i],du))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class ll extends xa{constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,q(u,"persistent")&&(u.persistent=!0),q(u,"ignoreInitial")&&(u.ignoreInitial=!1),q(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),q(u,"interval")&&(u.interval=100),q(u,"binaryInterval")&&(u.binaryInterval=300),q(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,q(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),lr.canUse()||(u.useFsEvents=!1),q(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=tl),ul&&(u.usePolling=!0);const r=process.env.CHOKIDAR_USEPOLLING;if(r!==void 0){const o=r.toLowerCase();o==="false"||o==="0"?u.usePolling=!1:o==="true"||o==="1"?u.usePolling=!0:u.usePolling=!!o}const s=process.env.CHOKIDAR_INTERVAL;s&&(u.interval=Number.parseInt(s,10)),q(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),q(u,"followSymlinks")&&(u.followSymlinks=!0),q(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=Cu(u.ignored));let D=0;this._emitReady=()=>{D++,D>=this._readyCount&&(this._emitReady=Ja,this._readyEmitted=!0,process.nextTick(()=>this.emit(Ia)))},this._emitRaw=(...o)=>this.emit(Wa,...o),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new lr(this):this._nodeFsHandler=new La(this),Object.freeze(u)}add(e,u,n){const{cwd:r,disableGlobbing:s}=this.options;this.closed=!1;let i=Er(e);return r&&(i=i.map(D=>{const o=sl(D,r);return s||!ou(D)?o:Pa(o)})),i=i.filter(D=>D.startsWith(fu)?(this._ignoredPaths.add(D.slice(1)),!1):(this._ignoredPaths.delete(D),this._ignoredPaths.delete(D+hu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount*=2),i.forEach(D=>this._fsEventsHandler._addToFsEvents(D))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async D=>{const o=await this._nodeFsHandler._addToNodeFs(D,!n,0,0,u);return o&&this._emitReady(),o})).then(D=>{this.closed||D.filter(o=>o).forEach(o=>{this.add(v.dirname(o),v.basename(u||o))})})),this}unwatch(e){if(this.closed)return this;const u=Er(e),{cwd:n}=this.options;return u.forEach(r=>{!v.isAbsolute(r)&&!this._closers.has(r)&&(n&&(r=v.join(n,r)),r=v.resolve(r)),this._closePath(r),this._ignoredPaths.add(r),this._watched.has(r)&&this._ignoredPaths.add(r+hu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(n=>{const r=n();r instanceof Promise&&e.push(r)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,n)=>{const r=this.options.cwd?v.relative(this.options.cwd,n):n;e[r||hr]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==lu&&this.emit(au,...u)}async _emit(e,u,n,r,s){if(this.closed)return;const i=this.options;el&&(u=v.normalize(u)),i.cwd&&(u=v.relative(i.cwd,u));const D=[e,u];s!==void 0?D.push(n,r,s):r!==void 0?D.push(n,r):n!==void 0&&D.push(n);const o=i.awaitWriteFinish;let a;if(o&&(a=this._pendingWrites.get(u)))return a.lastChange=new Date,this;if(i.atomic){if(e===cr)return this._pendingUnlinks.set(u,D),setTimeout(()=>{this._pendingUnlinks.forEach((c,f)=>{this.emit(...c),this.emit(au,...c),this._pendingUnlinks.delete(f)})},typeof i.atomic=="number"?i.atomic:100),this;e===it&&this._pendingUnlinks.has(u)&&(e=D[0]=Oe,this._pendingUnlinks.delete(u))}if(o&&(e===it||e===Oe)&&this._readyEmitted){const c=(f,l)=>{f?(e=D[0]=lu,D[1]=f,this.emitWithAll(e,D)):l&&(D.length>2?D[2]=l:D.push(l),this.emitWithAll(e,D))};return this._awaitWriteFinish(u,o.stabilityThreshold,e,c),this}if(e===Oe&&!this._throttle(Oe,u,50))return this;if(i.alwaysStat&&n===void 0&&(e===it||e===ka||e===Oe)){const c=i.cwd?v.join(i.cwd,u):u;let f;try{f=await nl(c)}catch{}if(!f||this.closed)return;D.push(f)}return this.emitWithAll(e,D),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(lu,e),e||this.closed}_throttle(e,u,n){this._throttled.has(e)||this._throttled.set(e,new Map);const r=this._throttled.get(e),s=r.get(u);if(s)return s.count++,!1;let i;const D=()=>{const a=r.get(u),c=a?a.count:0;return r.delete(u),clearTimeout(i),a&&clearTimeout(a.timeoutObject),c};i=setTimeout(D,n);const o={timeoutObject:i,clear:D,count:0};return r.set(u,o),o}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,n,r){let s,i=e;this.options.cwd&&!v.isAbsolute(e)&&(i=v.join(this.options.cwd,e));const D=new Date,o=a=>{iu.stat(i,(c,f)=>{if(c||!this._pendingWrites.has(e)){c&&c.code!=="ENOENT"&&r(c);return}const l=Number(new Date);a&&f.size!==a.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),r(void 0,f)):s=setTimeout(o,this.options.awaitWriteFinish.pollInterval,f)})};this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:D,cancelWait:()=>(this._pendingWrites.delete(e),clearTimeout(s),n)}),s=setTimeout(o,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&Va.test(e))return!0;if(!this._userIgnored){const{cwd:n}=this.options,r=this.options.ignored,s=r&&r.map(Fr(n)),i=Cu(s).filter(o=>typeof o===Eu&&!ou(o)).map(o=>o+hu),D=this._getGlobIgnored().map(Fr(n)).concat(s,i);this._userIgnored=Du(D,void 0,du)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const n=u||this.options.disableGlobbing||!ou(e)?e:Na(e),r=this.options.followSymlinks;return new al(e,n,r,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=v.resolve(e);return this._watched.has(u)||this._watched.set(u,new il(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const n=(e&&Number.parseInt(e.mode,10))&511;return!!(4&Number.parseInt(n.toString(8)[0],10))}_remove(e,u,n){const r=v.join(e,u),s=v.resolve(r);if(n=n!=null?n:this._watched.has(r)||this._watched.has(s),!this._throttle("remove",r,100))return;!n&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(r).getChildren().forEach(l=>this._remove(r,l));const o=this._getWatchedDir(e),a=o.has(u);o.remove(u),this._symlinkPaths.has(s)&&this._symlinkPaths.delete(s);let c=r;if(this.options.cwd&&(c=v.relative(this.options.cwd,r)),this.options.awaitWriteFinish&&this._pendingWrites.has(c)&&this._pendingWrites.get(c).cancelWait()===it)return;this._watched.delete(r),this._watched.delete(s);const f=n?Ma:cr;a&&!this._isIgnored(r)&&this._emit(f,r),this.options.useFsEvents||this._closePath(r)}_closePath(e){this._closeFile(e);const u=v.dirname(e);this._getWatchedDir(u).remove(v.basename(e))}_closeFile(e){const u=this._closers.get(e);u&&(u.forEach(n=>n()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let n=this._closers.get(e);n||(n=[],this._closers.set(e,n)),n.push(u)}_readdirp(e,u){if(this.closed)return;const n={type:au,alwaysStat:!0,lstat:!0,...u};let r=Oa(e,n);return this._streams.add(r),r.once(Ga,()=>{r=void 0}),r.once(ja,()=>{r&&(this._streams.delete(r),r=void 0)}),r}}const cl=(t,e)=>{const u=new ll(e);return u.add(t),u};var fl=cl;const Fu=(t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}};function gr(t,e=process.argv.slice(2)){return wu(t,e,{ignore:Fu()}),e}let fe=!0;const _e=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let Ne=0;if(_e.process&&_e.process.env&&_e.process.stdout){const{FORCE_COLOR:t,NODE_DISABLE_COLORS:e,NO_COLOR:u,TERM:n,COLORTERM:r}=_e.process.env;e||u||t==="0"?fe=!1:t==="1"||t==="2"||t==="3"?fe=!0:n==="dumb"?fe=!1:"CI"in _e.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(s=>s in _e.process.env)?fe=!0:fe=process.stdout.isTTY,fe&&(process.platform==="win32"||r&&(r==="truecolor"||r==="24bit")?Ne=3:n&&(n.endsWith("-256color")||n.endsWith("256"))?Ne=2:Ne=1)}let _r={enabled:fe,supportLevel:Ne};function mr(t,e,u=1){const n=`\x1B[${t}m`,r=`\x1B[${e}m`,s=new RegExp(`\\x1b\\[${e}m`,"g");return i=>_r.enabled&&_r.supportLevel>=u?n+(""+i).replace(s,n)+r:""+i}const hl=mr(90,39),dl=mr(96,39),El=()=>new Date().toLocaleTimeString(),gu=(...t)=>console.log(hl(El()),dl("[tsx]"),...t),pl="\x1Bc";function Cl(t,e){let u;return()=>{u&&clearTimeout(u),u=setTimeout(()=>t(),e)}}const Fl=async t=>{const e=new Promise(u=>{t.on("exit",u)});t.kill(),await e},Ar={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched"}},gl=ni({name:"watch",parameters:["<script path>"],flags:Ar,help:{description:"Run the script and watch for changes"},ignoreArgv:Fu(!1)},t=>{const e=gr(Ar,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,ignore:t.flags.ignore,ipc:!0};let n;const r=()=>{const c=sn(e,u);return c.on("message",f=>{if(f&&typeof f=="object"&&"type"in f&&f.type==="dependency"&&"path"in f&&typeof f.path=="string"){const l=f.path.startsWith("file:")?_u.fileURLToPath(f.path):f.path;K.default.isAbsolute(l)&&a.add(l)}}),c};let s=!1;const i=Cl(async()=>{if(s){gu("forcing restart"),n.kill("SIGKILL");return}(n==null?void 0:n.exitCode)===null?(gu("restarting"),s=!0,await Fl(n),s=!1):gu("rerunning"),u.clearScreen&&process.stdout.write(pl),n=r()},100);i();function D(c){process.exit(128+mu.constants.signals[c])}function o(c){n&&n.exitCode===null?(n.on("close",()=>D(c)),n.kill(c)):D(c)}process.once("SIGINT",o),process.once("SIGTERM",o);const a=fl(t._,{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.ignore],ignorePermissionErrors:!0}).on("all",i);process.stdin.on("data",i)}),yr={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};ui({name:"tsx",parameters:["[script path]"],commands:[gl],flags:{...yr,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:Fu()},t=>{t.flags.version?process.stdout.write(`tsx v${vr.version}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e=sn(gr(yr),{noCache:!!t.flags.noCache,tsconfigPath:t.flags.tsconfig}),u=async n=>{await Promise.race([new Promise(s=>{function i(D){D&&D.type==="kill"&&(s(D.signal),e.off("message",i))}e.on("message",i)}),new Promise(s=>{setTimeout(s,10)})])||e.kill(n)};process.on("SIGINT",u),process.on("SIGTERM",u),e.on("close",n=>process.exit(n))});
