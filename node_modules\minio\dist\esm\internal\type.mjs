// nodejs IncomingHttpHeaders is Record<string, string | string[]>, but it's actually this:

export let ENCRYPTION_TYPES = /*#__PURE__*/function (ENCRYPTION_TYPES) {
  ENCRYPTION_TYPES["SSEC"] = "SSE-C";
  ENCRYPTION_TYPES["KMS"] = "KMS";
  return ENCRYPTION_TYPES;
}({});
export let RETENTION_MODES = /*#__PURE__*/function (RETENTION_MODES) {
  RETENTION_MODES["GOVERNANCE"] = "GOVERNANCE";
  RETENTION_MODES["COMPLIANCE"] = "COMPLIANCE";
  return RETENTION_MODES;
}({});
export let RETENTION_VALIDITY_UNITS = /*#__PURE__*/function (RETENTION_VALIDITY_UNITS) {
  RETENTION_VALIDITY_UNITS["DAYS"] = "Days";
  RETENTION_VALIDITY_UNITS["YEARS"] = "Years";
  return RETENTION_VALIDITY_UNITS;
}({});
export let LEGAL_HOLD_STATUS = /*#__PURE__*/function (LEGAL_HOLD_STATUS) {
  LEGAL_HOLD_STATUS["ENABLED"] = "ON";
  LEGAL_HOLD_STATUS["DISABLED"] = "OFF";
  return LEGAL_HOLD_STATUS;
}({});

/* Replication Config types */

/* Replication Config types */

/**
 * @deprecated keep for backward compatible, use `LEGAL_HOLD_STATUS` instead
 */

/** List object api types **/ // Common types
/** List object api types **/
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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