"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDAO = void 0;
/**
 * Data Access Object for User model
 */
class UserDAO {
    constructor(pool) {
        this.pool = pool;
    }
    /**
     * Create a new user
     * @param user User object
     * @returns Created user
     */
    async create(user) {
        const query = `
      INSERT INTO users (
        id, email, username, password_hash, 
        two_factor_enabled, two_factor_secret, 
        two_factor_backup_codes, two_factor_last_used,
        created_at, updated_at, last_login_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;
        const values = [
            user.id,
            user.email,
            user.username,
            user.passwordHash,
            user.twoFactorEnabled,
            user.twoFactorSecret || null,
            user.twoFactorBackupCodes || null,
            user.twoFactorLastUsed || null,
            user.createdAt,
            user.updatedAt,
            user.lastLoginAt
        ];
        const result = await this.pool.query(query, values);
        return this._mapRowToUser(result.rows[0]);
    }
    /**
     * Find user by ID
     * @param id User ID
     * @returns User object or null if not found
     */
    async findById(id) {
        const query = 'SELECT * FROM users WHERE id = $1';
        const result = await this.pool.query(query, [id]);
        if (result.rows.length === 0) {
            return null;
        }
        return this._mapRowToUser(result.rows[0]);
    }
    /**
     * Find user by email
     * @param email User email
     * @returns User object or null if not found
     */
    async findByEmail(email) {
        const query = 'SELECT * FROM users WHERE email = $1';
        const result = await this.pool.query(query, [email]);
        if (result.rows.length === 0) {
            return null;
        }
        return this._mapRowToUser(result.rows[0]);
    }
    /**
     * Update user
     * @param user User object
     * @returns Updated user
     */
    async update(user) {
        const query = `
      UPDATE users
      SET 
        email = $1,
        username = $2,
        password_hash = $3,
        two_factor_enabled = $4,
        two_factor_secret = $5,
        two_factor_backup_codes = $6,
        two_factor_last_used = $7,
        updated_at = $8,
        last_login_at = $9
      WHERE id = $10
      RETURNING *
    `;
        const values = [
            user.email,
            user.username,
            user.passwordHash,
            user.twoFactorEnabled,
            user.twoFactorSecret || null,
            user.twoFactorBackupCodes || null,
            user.twoFactorLastUsed || null,
            new Date(), // updated_at
            user.lastLoginAt,
            user.id
        ];
        const result = await this.pool.query(query, values);
        return this._mapRowToUser(result.rows[0]);
    }
    /**
     * Map database row to User model
     * @param row Database row
     * @returns User model
     * @private
     */
    _mapRowToUser(row) {
        return {
            id: row.id,
            email: row.email,
            username: row.username,
            passwordHash: row.password_hash,
            twoFactorEnabled: row.two_factor_enabled,
            twoFactorSecret: row.two_factor_secret,
            twoFactorBackupCodes: row.two_factor_backup_codes,
            twoFactorLastUsed: row.two_factor_last_used,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            lastLoginAt: row.last_login_at
        };
    }
}
exports.UserDAO = UserDAO;
//# sourceMappingURL=user.dao.js.map