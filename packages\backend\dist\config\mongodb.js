"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoDB = void 0;
exports.createMongoDBFromEnv = createMongoDBFromEnv;
const mongodb_1 = require("mongodb");
class MongoDB {
    constructor(config) {
        this.client = new mongodb_1.MongoClient(config.uri);
        this.db = this.client.db(config.database);
    }
    static getInstance(config) {
        if (!MongoDB.instance) {
            if (!config) {
                throw new Error('MongoDB config required for first initialization');
            }
            MongoDB.instance = new MongoDB(config);
        }
        return MongoDB.instance;
    }
    async connect() {
        try {
            await this.client.connect();
            console.log('Connected to MongoDB successfully');
        }
        catch (error) {
            console.error('MongoDB connection failed:', error);
            throw error;
        }
    }
    async testConnection() {
        try {
            await this.client.db('admin').command({ ping: 1 });
            return true;
        }
        catch (error) {
            console.error('MongoDB connection test failed:', error);
            return false;
        }
    }
    getDb() {
        return this.db;
    }
    getCollection(name) {
        return this.db.collection(name);
    }
    async close() {
        await this.client.close();
    }
    async createIndexes() {
        try {
            // File metadata indexes
            const fileMetadataCollection = this.getCollection('file_metadata');
            await fileMetadataCollection.createIndexes([
                { key: { userId: 1 }, name: 'idx_file_metadata_userId' },
                { key: { folderId: 1 }, name: 'idx_file_metadata_folderId' },
                { key: { filename: 1 }, name: 'idx_file_metadata_filename' },
                { key: { checksum: 1 }, name: 'idx_file_metadata_checksum' },
                { key: { uploadedAt: -1 }, name: 'idx_file_metadata_uploadedAt' },
                { key: { tags: 1 }, name: 'idx_file_metadata_tags' },
                { key: { userId: 1, folderId: 1 }, name: 'idx_file_metadata_userId_folderId' },
                { key: { userId: 1, filename: 'text' }, name: 'idx_file_metadata_userId_filename_text' }
            ]);
            // Folder indexes
            const folderCollection = this.getCollection('folders');
            await folderCollection.createIndexes([
                { key: { userId: 1 }, name: 'idx_folders_userId' },
                { key: { parentId: 1 }, name: 'idx_folders_parentId' },
                { key: { path: 1 }, name: 'idx_folders_path' },
                { key: { userId: 1, parentId: 1 }, name: 'idx_folders_userId_parentId' },
                {
                    key: { userId: 1, parentId: 1, name: 1 },
                    unique: true,
                    name: 'idx_folders_unique_name_per_parent',
                    partialFilterExpression: { name: { $exists: true } }
                }
            ]);
            // Share link indexes
            const shareLinkCollection = this.getCollection('share_links');
            await shareLinkCollection.createIndexes([
                { key: { token: 1 }, unique: true, name: 'idx_share_links_token_unique' },
                { key: { fileId: 1 }, name: 'idx_share_links_fileId' },
                { key: { userId: 1 }, name: 'idx_share_links_userId' },
                { key: { createdAt: -1 }, name: 'idx_share_links_createdAt' },
                {
                    key: { expiresAt: 1 },
                    name: 'idx_share_links_expiresAt_ttl',
                    expireAfterSeconds: 0,
                    partialFilterExpression: { expiresAt: { $exists: true } }
                }
            ]);
            console.log('MongoDB indexes created successfully');
        }
        catch (error) {
            console.error('Failed to create MongoDB indexes:', error);
            throw error;
        }
    }
    async validateCollections() {
        try {
            // Validate file_metadata collection
            const fileMetadataCollection = this.getCollection('file_metadata');
            await fileMetadataCollection.createIndex({ userId: 1, filename: 1 }, {
                name: 'idx_file_metadata_userId_filename_sparse',
                sparse: true
            });
            // Create validation rules for file_metadata
            await this.db.command({
                collMod: 'file_metadata',
                validator: {
                    $jsonSchema: {
                        bsonType: 'object',
                        required: ['userId', 'filename', 'originalName', 'mimeType', 'size', 'checksum', 'uploadedAt', 'modifiedAt'],
                        properties: {
                            userId: { bsonType: 'string', minLength: 1 },
                            filename: { bsonType: 'string', minLength: 1 },
                            originalName: { bsonType: 'string', minLength: 1 },
                            mimeType: { bsonType: 'string', minLength: 1 },
                            size: { bsonType: 'number', minimum: 0 },
                            checksum: { bsonType: 'string', minLength: 1 },
                            folderId: { bsonType: ['string', 'null'] },
                            isPublic: { bsonType: 'bool' },
                            uploadedAt: { bsonType: 'date' },
                            modifiedAt: { bsonType: 'date' },
                            tags: { bsonType: 'array', items: { bsonType: 'string' } },
                            storageNodes: {
                                bsonType: 'array',
                                items: {
                                    bsonType: 'object',
                                    required: ['id', 'url', 'region', 'isHealthy'],
                                    properties: {
                                        id: { bsonType: 'string' },
                                        url: { bsonType: 'string' },
                                        region: { bsonType: 'string' },
                                        isHealthy: { bsonType: 'bool' }
                                    }
                                }
                            }
                        }
                    }
                },
                validationLevel: 'moderate',
                validationAction: 'warn'
            });
            // Create validation rules for folders
            await this.db.command({
                collMod: 'folders',
                validator: {
                    $jsonSchema: {
                        bsonType: 'object',
                        required: ['userId', 'name', 'path', 'createdAt', 'modifiedAt'],
                        properties: {
                            userId: { bsonType: 'string', minLength: 1 },
                            name: { bsonType: 'string', minLength: 1 },
                            parentId: { bsonType: ['string', 'null'] },
                            path: { bsonType: 'string', minLength: 1 },
                            createdAt: { bsonType: 'date' },
                            modifiedAt: { bsonType: 'date' }
                        }
                    }
                },
                validationLevel: 'moderate',
                validationAction: 'warn'
            });
            // Create validation rules for share_links
            await this.db.command({
                collMod: 'share_links',
                validator: {
                    $jsonSchema: {
                        bsonType: 'object',
                        required: ['fileId', 'userId', 'token', 'permissions', 'downloadCount', 'createdAt'],
                        properties: {
                            fileId: { bsonType: 'string', minLength: 1 },
                            userId: { bsonType: 'string', minLength: 1 },
                            token: { bsonType: 'string', minLength: 1 },
                            permissions: {
                                bsonType: 'array',
                                items: {
                                    bsonType: 'object',
                                    required: ['type', 'granted'],
                                    properties: {
                                        type: { enum: ['read', 'write', 'delete'] },
                                        granted: { bsonType: 'bool' }
                                    }
                                }
                            },
                            expiresAt: { bsonType: ['date', 'null'] },
                            password: { bsonType: ['string', 'null'] },
                            downloadCount: { bsonType: 'number', minimum: 0 },
                            maxDownloads: { bsonType: ['number', 'null'], minimum: 1 },
                            createdAt: { bsonType: 'date' }
                        }
                    }
                },
                validationLevel: 'moderate',
                validationAction: 'warn'
            });
            console.log('MongoDB collection validation rules applied successfully');
        }
        catch (error) {
            console.error('Failed to apply MongoDB validation rules:', error);
            // Don't throw error as validation rules are optional
        }
    }
}
exports.MongoDB = MongoDB;
// Factory function to create MongoDB instance from environment variables
function createMongoDBFromEnv() {
    const config = {
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
        database: process.env.MONGODB_DB || 'cloud_storage'
    };
    return MongoDB.getInstance(config);
}
//# sourceMappingURL=mongodb.js.map