# 云盘应用设计文档

## 概述

本设计文档描述了一个现代化的云盘应用架构，采用微服务架构模式，支持分布式存储、多端同步、图床功能等核心特性。系统设计遵循高可用、高性能、高安全性的原则。

## 架构

### 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        WEB[Web应用]
        MOBILE[移动应用]
        DESKTOP[桌面应用]
    end
    
    subgraph "网关层"
        LB[负载均衡器]
        GATEWAY[API网关]
        CDN[CDN网络]
    end
    
    subgraph "服务层"
        AUTH[认证服务]
        FILE[文件服务]
        SYNC[同步服务]
        IMAGE[图床服务]
        NOTIFY[通知服务]
    end
    
    subgraph "数据层"
        REDIS[Redis缓存]
        POSTGRES[PostgreSQL]
        MONGO[MongoDB]
        STORAGE[分布式存储]
    end
    
    subgraph "基础设施"
        MQ[消息队列]
        MONITOR[监控系统]
        LOG[日志系统]
    end
    
    WEB --> LB
    MOBILE --> LB
    DESKTOP --> LB
    LB --> GATEWAY
    GATEWAY --> AUTH
    GATEWAY --> FILE
    GATEWAY --> SYNC
    GATEWAY --> IMAGE
    GATEWAY --> NOTIFY
    
    AUTH --> REDIS
    AUTH --> POSTGRES
    FILE --> MONGO
    FILE --> STORAGE
    SYNC --> MQ
    SYNC --> REDIS
    IMAGE --> CDN
    IMAGE --> STORAGE
    NOTIFY --> MQ
```

### 技术栈选择

- **前端框架**: React + TypeScript
- **UI样式**: Tailwind CSS
- **主题设计**: 
  - 暗黑模式: Dracula Theme 风格
  - 明亮模式: 基于暗黑模式调整的对应配色
- **后端框架**: Node.js + Express / Python + FastAPI
- **数据库**: PostgreSQL (用户数据) + MongoDB (文件元数据)
- **缓存**: Redis (会话、缓存)
- **消息队列**: RabbitMQ / Apache Kafka
- **存储**: MinIO / AWS S3 兼容存储
- **搜索**: Elasticsearch
- **监控**: Prometheus + Grafana

### 前端UI设计规范

#### 主题配色方案

**Dracula暗黑主题**:
```css
:root[data-theme="dark"] {
  --bg-primary: #282a36;
  --bg-secondary: #44475a;
  --text-primary: #f8f8f2;
  --text-secondary: #6272a4;
  --accent-purple: #bd93f9;
  --accent-pink: #ff79c6;
  --accent-green: #50fa7b;
  --accent-orange: #ffb86c;
  --accent-red: #ff5555;
  --accent-yellow: #f1fa8c;
  --accent-cyan: #8be9fd;
}
```

**明亮主题** (基于Dracula调整):
```css
:root[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --accent-purple: #805ad5;
  --accent-pink: #d53f8c;
  --accent-green: #38a169;
  --accent-orange: #dd6b20;
  --accent-red: #e53e3e;
  --accent-yellow: #d69e2e;
  --accent-cyan: #0bc5ea;
}
```

#### 响应式布局
- **桌面端**: 侧边栏 + 主内容区域布局
- **平板端**: 可折叠侧边栏
- **移动端**: 底部导航栏 + 全屏内容

## 组件和接口

### 1. 认证服务 (Authentication Service)

**职责**: 用户认证、授权、会话管理

**核心接口**:
```typescript
interface AuthService {
  // 用户注册
  register(userData: UserRegistration): Promise<AuthResult>
  
  // 用户登录
  login(credentials: LoginCredentials): Promise<AuthResult>
  
  // 刷新令牌
  refreshToken(refreshToken: string): Promise<TokenPair>
  
  // 多设备会话管理
  getActiveSessions(userId: string): Promise<Session[]>
  revokeSession(sessionId: string): Promise<void>
  
  // 两步验证
  enableTwoFactor(userId: string): Promise<TwoFactorSetup>
  verifyTwoFactor(userId: string, code: string): Promise<boolean>
}
```

**数据模型**:
```typescript
interface User {
  id: string
  email: string
  passwordHash: string
  twoFactorSecret?: string
  createdAt: Date
  lastLoginAt: Date
}

interface Session {
  id: string
  userId: string
  deviceInfo: DeviceInfo
  ipAddress: string
  expiresAt: Date
  isActive: boolean
}
```

### 2. 文件服务 (File Service)

**职责**: 文件上传、下载、管理、搜索

**核心接口**:
```typescript
interface FileService {
  // 文件上传
  uploadFile(file: FileUpload, metadata: FileMetadata): Promise<FileInfo>
  
  // 批量上传
  uploadFiles(files: FileUpload[]): Promise<FileInfo[]>
  
  // 文件下载
  downloadFile(fileId: string, userId: string): Promise<FileStream>
  
  // 文件列表
  listFiles(userId: string, folderId?: string, pagination: Pagination): Promise<FileList>
  
  // 文件搜索
  searchFiles(userId: string, query: SearchQuery): Promise<FileList>
  
  // 文件夹操作
  createFolder(userId: string, folderData: FolderData): Promise<Folder>
  moveFile(fileId: string, targetFolderId: string): Promise<void>
  
  // 文件分享
  shareFile(fileId: string, shareOptions: ShareOptions): Promise<ShareLink>
}
```

### 3. 同步服务 (Sync Service)

**职责**: 实时同步、冲突解决、版本控制

**核心接口**:
```typescript
interface SyncService {
  // 获取同步状态
  getSyncStatus(userId: string, deviceId: string): Promise<SyncStatus>
  
  // 推送变更
  pushChanges(userId: string, changes: FileChange[]): Promise<SyncResult>
  
  // 拉取变更
  pullChanges(userId: string, lastSyncTime: Date): Promise<FileChange[]>
  
  // 冲突解决
  resolveConflict(conflictId: string, resolution: ConflictResolution): Promise<void>
  
  // 实时通知
  subscribeToChanges(userId: string): Promise<EventStream>
}
```

### 4. 图床服务 (Image Service)

**职责**: 图片处理、缩略图生成、CDN集成

**核心接口**:
```typescript
interface ImageService {
  // 图片上传
  uploadImage(image: ImageUpload): Promise<ImageInfo>
  
  // 批量图片上传
  uploadImages(images: ImageUpload[]): Promise<ImageInfo[]>
  
  // 生成缩略图
  generateThumbnails(imageId: string, sizes: ThumbnailSize[]): Promise<Thumbnail[]>
  
  // 获取图片URL
  getImageUrl(imageId: string, size?: string): Promise<string>
  
  // 图片格式转换
  convertImage(imageId: string, targetFormat: ImageFormat): Promise<ImageInfo>
}
```

## 数据模型

### 用户相关数据模型

```typescript
interface User {
  id: string
  email: string
  username: string
  passwordHash: string
  storageQuota: number
  usedStorage: number
  twoFactorEnabled: boolean
  twoFactorSecret?: string
  createdAt: Date
  updatedAt: Date
}

interface UserProfile {
  userId: string
  displayName: string
  avatar?: string
  timezone: string
  language: string
  preferences: UserPreferences
}
```

### 文件相关数据模型

```typescript
interface FileMetadata {
  id: string
  userId: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  checksum: string
  folderId?: string
  isPublic: boolean
  uploadedAt: Date
  modifiedAt: Date
  tags: string[]
  storageNodes: StorageNode[]
}

interface Folder {
  id: string
  userId: string
  name: string
  parentId?: string
  path: string
  createdAt: Date
  modifiedAt: Date
}

interface ShareLink {
  id: string
  fileId: string
  userId: string
  token: string
  permissions: Permission[]
  expiresAt?: Date
  password?: string
  downloadCount: number
  maxDownloads?: number
  createdAt: Date
}
```

### 同步相关数据模型

```typescript
interface SyncEvent {
  id: string
  userId: string
  deviceId: string
  eventType: 'CREATE' | 'UPDATE' | 'DELETE' | 'MOVE'
  resourceType: 'FILE' | 'FOLDER'
  resourceId: string
  timestamp: Date
  checksum?: string
  conflictId?: string
}

interface ConflictResolution {
  conflictId: string
  strategy: 'KEEP_LOCAL' | 'KEEP_REMOTE' | 'MERGE' | 'RENAME'
  resolvedBy: string
  resolvedAt: Date
}
```

## 错误处理

### 错误分类和处理策略

```typescript
enum ErrorType {
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SYNC_CONFLICT = 'SYNC_CONFLICT',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

interface ErrorResponse {
  error: {
    type: ErrorType
    message: string
    code: string
    details?: any
    timestamp: Date
    requestId: string
  }
}
```

### 重试机制

- **网络错误**: 指数退避重试，最多3次
- **存储错误**: 立即重试其他存储节点
- **同步冲突**: 暂停同步，等待用户解决
- **配额超限**: 立即停止上传，通知用户

### 降级策略

- **存储服务不可用**: 启用本地缓存模式
- **同步服务异常**: 切换到轮询模式
- **图片处理失败**: 返回原图或默认缩略图
- **搜索服务故障**: 使用基础文件名搜索

## 测试策略

### 单元测试
- **覆盖率目标**: 90%以上
- **测试框架**: Jest (Node.js) / pytest (Python)
- **模拟依赖**: 数据库、外部API、文件系统

### 集成测试
- **API测试**: 使用Postman/Newman自动化测试
- **数据库测试**: 使用测试数据库进行集成测试
- **消息队列测试**: 验证异步消息处理

### 端到端测试
- **Web端**: Cypress自动化测试
- **移动端**: Appium自动化测试
- **性能测试**: JMeter压力测试

### 安全测试
- **认证测试**: 验证JWT令牌安全性
- **授权测试**: 确保权限控制正确
- **数据加密测试**: 验证存储和传输加密
- **渗透测试**: 定期进行安全漏洞扫描

### 分布式系统测试
- **一致性测试**: 验证数据一致性
- **分区容错测试**: 模拟网络分区
- **故障恢复测试**: 验证自动故障恢复
- **负载测试**: 验证系统扩展性