#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const minio_1 = require("minio");
const pg_1 = require("pg");
const mongodb_1 = require("mongodb");
const ioredis_1 = __importDefault(require("ioredis"));
const config_1 = __importDefault(require("../config"));
class HealthChecker {
    constructor() {
        this.services = [
            { name: 'PostgreSQL', status: 'checking' },
            { name: 'MongoDB', status: 'checking' },
            { name: 'Redis', status: 'checking' },
            { name: 'Min<PERSON>', status: 'checking' }
        ];
    }
    async checkPostgreSQL() {
        try {
            const client = new pg_1.Client({
                host: config_1.default.database.postgres.host,
                port: config_1.default.database.postgres.port,
                user: config_1.default.database.postgres.user,
                password: config_1.default.database.postgres.password,
                database: config_1.default.database.postgres.database,
            });
            await client.connect();
            await client.query('SELECT 1');
            await client.end();
            this.updateServiceStatus('PostgreSQL', 'healthy');
            return true;
        }
        catch (error) {
            this.updateServiceStatus('PostgreSQL', 'unhealthy', `连接失败: ${error}`);
            return false;
        }
    }
    async checkMongoDB() {
        try {
            const client = new mongodb_1.MongoClient(config_1.default.database.mongodb.uri);
            await client.connect();
            await client.db(config_1.default.database.mongodb.database).admin().ping();
            await client.close();
            this.updateServiceStatus('MongoDB', 'healthy');
            return true;
        }
        catch (error) {
            this.updateServiceStatus('MongoDB', 'unhealthy', `连接失败: ${error}`);
            return false;
        }
    }
    async checkRedis() {
        try {
            const redis = new ioredis_1.default({
                host: config_1.default.database.redis.host,
                port: config_1.default.database.redis.port,
                password: config_1.default.database.redis.password,
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3,
            });
            await redis.ping();
            redis.disconnect();
            this.updateServiceStatus('Redis', 'healthy');
            return true;
        }
        catch (error) {
            this.updateServiceStatus('Redis', 'unhealthy', `连接失败: ${error}`);
            return false;
        }
    }
    async checkMinIO() {
        try {
            const minioClient = new minio_1.Client({
                endPoint: config_1.default.storage.minio.endpoint,
                port: config_1.default.storage.minio.port,
                useSSL: config_1.default.storage.minio.useSSL,
                accessKey: config_1.default.storage.minio.accessKey,
                secretKey: config_1.default.storage.minio.secretKey,
                region: config_1.default.storage.minio.region
            });
            await minioClient.listBuckets();
            // 确保存储桶存在
            const bucketExists = await minioClient.bucketExists(config_1.default.storage.minio.bucket);
            if (!bucketExists) {
                await minioClient.makeBucket(config_1.default.storage.minio.bucket, config_1.default.storage.minio.region);
                console.log(`   📦 创建存储桶: ${config_1.default.storage.minio.bucket}`);
            }
            this.updateServiceStatus('MinIO', 'healthy');
            return true;
        }
        catch (error) {
            this.updateServiceStatus('MinIO', 'unhealthy', `连接失败: ${error}`);
            return false;
        }
    }
    updateServiceStatus(name, status, message) {
        const service = this.services.find(s => s.name === name);
        if (service) {
            service.status = status;
            service.message = message;
        }
    }
    printStatus() {
        console.clear();
        console.log('🏥 服务健康检查状态:\n');
        this.services.forEach(service => {
            const statusIcon = service.status === 'healthy' ? '✅' :
                service.status === 'unhealthy' ? '❌' : '🔄';
            const statusText = service.status === 'healthy' ? '健康' :
                service.status === 'unhealthy' ? '不健康' : '检查中...';
            console.log(`${statusIcon} ${service.name}: ${statusText}`);
            if (service.message && service.status === 'unhealthy') {
                console.log(`   💬 ${service.message}`);
            }
        });
        console.log('');
    }
    async runHealthChecks() {
        console.log('🔍 开始健康检查...\n');
        const checks = [
            this.checkPostgreSQL(),
            this.checkMongoDB(),
            this.checkRedis(),
            this.checkMinIO()
        ];
        // 显示进度
        const interval = setInterval(() => {
            this.printStatus();
        }, 500);
        try {
            const results = await Promise.all(checks);
            clearInterval(interval);
            this.printStatus();
            const allHealthy = results.every(result => result);
            if (allHealthy) {
                console.log('🎉 所有服务都健康！准备启动应用...\n');
                return true;
            }
            else {
                console.log('❌ 部分服务不健康，请检查并修复后重试\n');
                console.log('💡 建议运行以下命令启动服务:');
                console.log('   docker-compose -f docker-compose.dev.yml up -d\n');
                return false;
            }
        }
        catch (error) {
            clearInterval(interval);
            console.error('健康检查过程中发生错误:', error);
            return false;
        }
    }
    async startApplication() {
        console.log('🚀 启动应用服务器...\n');
        const serverProcess = (0, child_process_1.spawn)('npm', ['run', 'dev'], {
            stdio: 'inherit',
            shell: true,
            cwd: process.cwd()
        });
        serverProcess.on('error', (error) => {
            console.error('启动应用失败:', error);
            process.exit(1);
        });
        // 处理优雅关闭
        process.on('SIGINT', () => {
            console.log('\n🛑 正在关闭应用...');
            serverProcess.kill('SIGINT');
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            console.log('\n🛑 正在关闭应用...');
            serverProcess.kill('SIGTERM');
            process.exit(0);
        });
    }
}
async function main() {
    const healthChecker = new HealthChecker();
    const isHealthy = await healthChecker.runHealthChecks();
    if (isHealthy) {
        await healthChecker.startApplication();
    }
    else {
        process.exit(1);
    }
}
// 运行主程序
main().catch(error => {
    console.error('启动过程中发生错误:', error);
    process.exit(1);
});
//# sourceMappingURL=start-with-health-check.js.map