#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
async function testUploadFix() {
    console.log('🧪 测试文件上传修复...\n');
    const baseURL = 'http://localhost:3001';
    // 1. 测试服务器连接
    console.log('1. 测试服务器连接:');
    try {
        const response = await axios_1.default.get(`${baseURL}/health`);
        console.log(`   ✅ 服务器响应: ${response.status}`);
    }
    catch (error) {
        console.log(`   ❌ 服务器连接失败: ${error}`);
        console.log('   💡 请确保后端服务正在运行: npm run dev');
        return;
    }
    // 2. 创建测试文件
    console.log('\n2. 创建测试文件:');
    const testFilePath = path_1.default.join(__dirname, 'test-upload.txt');
    const testContent = `测试文件内容 - ${new Date().toISOString()}`;
    try {
        fs_1.default.writeFileSync(testFilePath, testContent);
        console.log(`   ✅ 测试文件创建: ${testFilePath}`);
    }
    catch (error) {
        console.log(`   ❌ 测试文件创建失败: ${error}`);
        return;
    }
    // 3. 模拟用户登录获取token（这里使用模拟token）
    console.log('\n3. 获取认证令牌:');
    let authToken = '';
    try {
        // 尝试注册测试用户
        const registerResponse = await axios_1.default.post(`${baseURL}/api/auth/register`, {
            email: `test-${Date.now()}@example.com`,
            password: 'testpassword123',
            name: 'Test User'
        });
        authToken = registerResponse.data.data.accessToken;
        console.log(`   ✅ 测试用户注册成功`);
    }
    catch (error) {
        console.log(`   ⚠️  用户注册失败，尝试使用现有用户登录`);
        try {
            const loginResponse = await axios_1.default.post(`${baseURL}/api/auth/login`, {
                email: '<EMAIL>',
                password: 'testpassword123'
            });
            authToken = loginResponse.data.data.accessToken;
            console.log(`   ✅ 用户登录成功`);
        }
        catch (loginError) {
            console.log(`   ❌ 认证失败: ${loginError}`);
            console.log('   💡 请确保认证服务正常工作');
            return;
        }
    }
    // 4. 测试文件上传
    console.log('\n4. 测试文件上传:');
    try {
        const formData = new form_data_1.default();
        formData.append('file', fs_1.default.createReadStream(testFilePath));
        formData.append('isPublic', 'false');
        const uploadResponse = await axios_1.default.post(`${baseURL}/api/files/upload`, formData, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                ...formData.getHeaders()
            },
            timeout: 30000 // 30秒超时
        });
        console.log(`   ✅ 文件上传成功!`);
        console.log(`   📄 文件信息:`, {
            id: uploadResponse.data.data.id,
            filename: uploadResponse.data.data.filename,
            size: uploadResponse.data.data.size,
            mimeType: uploadResponse.data.data.mimeType
        });
        // 5. 测试文件列表
        console.log('\n5. 测试文件列表:');
        const listResponse = await axios_1.default.get(`${baseURL}/api/files/list`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        console.log(`   ✅ 文件列表获取成功`);
        console.log(`   📋 文件数量: ${listResponse.data.data.files.length}`);
        // 6. 测试文件下载
        console.log('\n6. 测试文件下载:');
        const fileId = uploadResponse.data.data.id;
        const downloadResponse = await axios_1.default.get(`${baseURL}/api/files/${fileId}/download`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            responseType: 'stream'
        });
        console.log(`   ✅ 文件下载成功`);
        console.log(`   📦 响应头:`, {
            contentType: downloadResponse.headers['content-type'],
            contentLength: downloadResponse.headers['content-length'],
            contentDisposition: downloadResponse.headers['content-disposition']
        });
    }
    catch (error) {
        console.log(`   ❌ 文件上传测试失败:`);
        if (axios_1.default.isAxiosError(error)) {
            console.log(`   📊 状态码: ${error.response?.status}`);
            console.log(`   📝 错误信息: ${error.response?.data?.message || error.message}`);
            if (error.response?.status === 503) {
                console.log('   💡 存储服务不可用，请检查MinIO服务');
                console.log('   💡 运行: docker-compose -f docker-compose.dev.yml up -d minio');
            }
            else if (error.response?.status === 401) {
                console.log('   💡 认证失败，请检查JWT令牌');
            }
            else if (error.response?.status === 413) {
                console.log('   💡 文件太大或存储配额不足');
            }
        }
        else {
            console.log(`   📝 错误详情: ${error}`);
        }
        return;
    }
    // 7. 清理测试文件
    console.log('\n7. 清理测试文件:');
    try {
        fs_1.default.unlinkSync(testFilePath);
        console.log(`   ✅ 测试文件清理完成`);
    }
    catch (error) {
        console.log(`   ⚠️  测试文件清理失败: ${error}`);
    }
    console.log('\n🎉 文件上传功能测试通过！');
    console.log('\n✅ 修复成功，文件上传功能正常工作');
}
// 运行测试
testUploadFix().catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
});
//# sourceMappingURL=test-upload-fix.js.map