/*
 * MinIO Javascript Library for Amazon S3 Compatible Cloud Storage, (C) 2015 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/// <reference lib="ES2022.Error" />

class ExtendableError extends Error {
  constructor(message, opt) {
    // error Option {cause?: unknown} is a 'nice to have',
    // don't use it internally
    super(message, opt);
    // set error name, otherwise it's always 'Error'
    this.name = this.constructor.name;
  }
}

/**
 * AnonymousRequestError is generated for anonymous keys on specific
 * APIs. NOTE: PresignedURL generation always requires access keys.
 */
export class AnonymousRequestError extends ExtendableError {}

/**
 * InvalidArgumentError is generated for all invalid arguments.
 */
export class InvalidArgumentError extends ExtendableError {}

/**
 * InvalidPortError is generated when a non integer value is provided
 * for ports.
 */
export class InvalidPortError extends ExtendableError {}

/**
 * InvalidEndpointError is generated when an invalid end point value is
 * provided which does not follow domain standards.
 */
export class InvalidEndpointError extends ExtendableError {}

/**
 * InvalidBucketNameError is generated when an invalid bucket name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/BucketRestrictions.html
 */
export class InvalidBucketNameError extends ExtendableError {}

/**
 * InvalidObjectNameError is generated when an invalid object name is
 * provided which does not follow AWS S3 specifications.
 * http://docs.aws.amazon.com/AmazonS3/latest/dev/UsingMetadata.html
 */
export class InvalidObjectNameError extends ExtendableError {}

/**
 * AccessKeyRequiredError generated by signature methods when access
 * key is not found.
 */
export class AccessKeyRequiredError extends ExtendableError {}

/**
 * SecretKeyRequiredError generated by signature methods when secret
 * key is not found.
 */
export class SecretKeyRequiredError extends ExtendableError {}

/**
 * ExpiresParamError generated when expires parameter value is not
 * well within stipulated limits.
 */
export class ExpiresParamError extends ExtendableError {}

/**
 * InvalidDateError generated when invalid date is found.
 */
export class InvalidDateError extends ExtendableError {}

/**
 * InvalidPrefixError generated when object prefix provided is invalid
 * or does not conform to AWS S3 object key restrictions.
 */
export class InvalidPrefixError extends ExtendableError {}

/**
 * InvalidBucketPolicyError generated when the given bucket policy is invalid.
 */
export class InvalidBucketPolicyError extends ExtendableError {}

/**
 * IncorrectSizeError generated when total data read mismatches with
 * the input size.
 */
export class IncorrectSizeError extends ExtendableError {}

/**
 * InvalidXMLError generated when an unknown XML is found.
 */
export class InvalidXMLError extends ExtendableError {}

/**
 * S3Error is generated for errors returned from S3 server.
 * see getErrorTransformer for details
 */
export class S3Error extends ExtendableError {}
export class IsValidBucketNameError extends ExtendableError {}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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