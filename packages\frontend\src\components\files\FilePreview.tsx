import React, { useState, useEffect } from 'react'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import { 
  ArrowDownTrayIcon,
  ShareIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { FileItem } from '../../pages/Files'
import { useI18n } from '../../contexts/I18nContext'
import { fileService } from '../../services/fileService'

interface FilePreviewProps {
  file: FileItem
  files: FileItem[]
  onClose: () => void
  onDownload: (file: FileItem) => void
  onShare: (file: FileItem) => void
  onNext?: () => void
  onPrevious?: () => void
}

const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  files,
  onClose,
  onDownload,
  onShare,
  onNext,
  onPrevious
}) => {
  const { t } = useI18n()
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Define file type checks first
  const isImage = file.mimeType?.startsWith('image/')
  const isVideo = file.mimeType?.startsWith('video/')
  const isAudio = file.mimeType?.startsWith('audio/')
  const isPdf = file.mimeType === 'application/pdf'
  const isText = file.mimeType?.startsWith('text/') || 
                 file.mimeType?.includes('json') ||
                 file.mimeType?.includes('xml')

  // Load preview URL for supported file types
  useEffect(() => {
    const loadPreview = async () => {
      // Only load preview for supported file types
      if (!isImage && !isVideo && !isAudio && !isPdf && !isText) return
      
      setLoading(true)
      setError(null)
      
      try {
        // Use preview API for better performance
        console.log('Loading preview for file:', file.id, file.name)
        const blob = await fileService.getFilePreview(file.id, {
          quality: 'medium',
          maxWidth: 1200,
          maxHeight: 800
        })
        const url = URL.createObjectURL(blob)
        setPreviewUrl(url)
      } catch (err) {
        console.error('Failed to load preview:', err)
        setError(t('preview.error'))
        
        // Fallback to thumbnail URL if available
        if (file.thumbnailUrl) {
          setPreviewUrl(file.thumbnailUrl)
        }
      } finally {
        setLoading(false)
      }
    }

    loadPreview()

    // Cleanup URL when component unmounts or file changes
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [file.id, isImage, isVideo, isAudio, isPdf, isText, t])

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return t('preview.fileSize')
    
    return fileService.formatFileSize(bytes)
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const currentIndex = files.findIndex(f => f.id === file.id)
  const canNavigate = files.length > 1

  return (
    <Modal isOpen={true} onClose={onClose} title={file.name} size="xl">
      <div className="flex flex-col h-[70vh] p-4">
        {/* Navigation */}
        {canNavigate && (
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={onPrevious}
              disabled={currentIndex <= 0}
            >
              <ChevronLeftIcon className="w-4 h-4 mr-1" />
              {t('preview.previous')}
            </Button>
            
            <span className="text-sm text-comment">
              {currentIndex + 1} / {files.length}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onNext}
              disabled={currentIndex >= files.length - 1}
            >
              {t('preview.next')}
              <ChevronRightIcon className="w-4 h-4 ml-1" />
            </Button>
          </div>
        )}

        {/* Preview Content */}
        <div className="flex-1 flex items-center justify-center bg-current-line rounded-lg overflow-hidden">
          {isImage && (
            <div className="w-full h-full flex items-center justify-center">
              {loading && (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-2"></div>
                  <p className="text-comment">{t('preview.loading')}</p>
                </div>
              )}
              
              {error && !previewUrl && (
                <div className="text-center text-red-400">
                  <div className="w-32 h-32 bg-current-line rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <span className="text-4xl">❌</span>
                  </div>
                  <p className="text-lg font-medium">{t('preview.error')}</p>
                  <p className="text-sm">{error}</p>
                </div>
              )}
              
              {previewUrl && (
                <img
                  src={previewUrl}
                  alt={file.name}
                  className="max-w-full max-h-full object-contain"
                  onError={() => setError('Failed to load image')}
                />
              )}
            </div>
          )}
          
          {isVideo && (
            <video
              controls
              className="max-w-full max-h-full"
              src={previewUrl || `/api/files/${file.id}/preview`}
            >
              {t('preview.noPreview')}
            </video>
          )}
          
          {isAudio && (
            <div className="text-center">
              <div className="w-32 h-32 bg-purple-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                <span className="text-4xl">🎵</span>
              </div>
              <audio
                controls
                className="w-full max-w-md"
                src={previewUrl || `/api/files/${file.id}/preview`}
              >
                {t('preview.noPreview')}
              </audio>
            </div>
          )}
          
          {isPdf && (
            <iframe
              src={previewUrl || `/api/files/${file.id}/preview`}
              className="w-full h-full border-0"
              title={file.name}
            />
          )}
          
          {isText && previewUrl && (
            <div className="w-full h-full p-4 overflow-auto">
              <iframe
                src={previewUrl}
                className="w-full h-full border-0"
                title={file.name}
              />
            </div>
          )}
          
          {isText && (
            <div className="w-full h-full p-4 overflow-auto">
              <pre className="text-sm text-primary whitespace-pre-wrap">
                {t('preview.loading')}
              </pre>
            </div>
          )}
          
          {!isImage && !isVideo && !isAudio && !isPdf && !isText && (
            <div className="text-center text-comment">
              <div className="w-32 h-32 bg-current-line rounded-lg flex items-center justify-center mb-4 mx-auto">
                <span className="text-4xl">📄</span>
              </div>
              <p className="text-lg font-medium">{t('preview.noPreview')}</p>
              <p className="text-sm">
                {t('preview.error')}
              </p>
            </div>
          )}
        </div>

        {/* File Info */}
        <div className="mt-4 p-4 bg-current-line rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-comment">{t('preview.fileSize')}:</span>
              <p className="text-primary font-medium">
                {formatFileSize(file.size)}
              </p>
            </div>
            
            <div>
              <span className="text-comment">{t('preview.fileType')}:</span>
              <p className="text-primary font-medium">
                {file.mimeType || 'Unknown'}
              </p>
            </div>
            
            <div>
              <span className="text-comment">{t('preview.modified')}:</span>
              <p className="text-primary font-medium">
                {formatDate(file.modifiedAt)}
              </p>
            </div>
            
            <div>
              <span className="text-comment">状态:</span>
              <p className="text-primary font-medium">
                {file.isShared ? t('preview.public') : t('preview.private')}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 mt-4 mb-4">
          <Button
            variant="outline"
            onClick={() => onShare(file)}
          >
            <ShareIcon className="w-4 h-4 mr-2" />
            {t('preview.share')}
          </Button>

          <Button
            variant="primary"
            onClick={() => onDownload(file)}
          >
            <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
            {t('preview.download')}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default FilePreview