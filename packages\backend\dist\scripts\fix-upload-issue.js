#!/usr/bin/env ts-node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const minio_1 = require("minio");
const config_1 = __importDefault(require("../config"));
async function fixUploadIssue() {
    console.log('🔧 开始修复文件上传问题...\n');
    // 1. 检查MinIO连接
    console.log('1. 检查MinIO连接:');
    const minioClient = new minio_1.Client({
        endPoint: config_1.default.storage.minio.endpoint,
        port: config_1.default.storage.minio.port,
        useSSL: config_1.default.storage.minio.useSSL,
        accessKey: config_1.default.storage.minio.accessKey,
        secretKey: config_1.default.storage.minio.secretKey,
        region: config_1.default.storage.minio.region
    });
    try {
        // 测试连接
        const buckets = await minioClient.listBuckets();
        console.log(`   ✅ MinIO连接成功，发现 ${buckets.length} 个存储桶`);
        buckets.forEach(bucket => {
            console.log(`   - 存储桶: ${bucket.name} (创建时间: ${bucket.creationDate})`);
        });
    }
    catch (error) {
        console.log(`   ❌ MinIO连接失败: ${error}`);
        console.log('   💡 请确保MinIO服务正在运行:');
        console.log('   💡 docker-compose -f docker-compose.dev.yml up -d minio');
        return false;
    }
    // 2. 确保存储桶存在
    console.log('\n2. 检查并创建存储桶:');
    const bucketName = config_1.default.storage.minio.bucket;
    try {
        const bucketExists = await minioClient.bucketExists(bucketName);
        if (!bucketExists) {
            console.log(`   📦 创建存储桶: ${bucketName}`);
            await minioClient.makeBucket(bucketName, config_1.default.storage.minio.region);
            console.log(`   ✅ 存储桶 ${bucketName} 创建成功`);
        }
        else {
            console.log(`   ✅ 存储桶 ${bucketName} 已存在`);
        }
        // 设置存储桶策略（允许读取）
        const policy = {
            Version: '2012-10-17',
            Statement: [
                {
                    Effect: 'Allow',
                    Principal: { AWS: ['*'] },
                    Action: ['s3:GetObject'],
                    Resource: [`arn:aws:s3:::${bucketName}/*`]
                }
            ]
        };
        await minioClient.setBucketPolicy(bucketName, JSON.stringify(policy));
        console.log(`   ✅ 存储桶策略设置完成`);
    }
    catch (error) {
        console.log(`   ❌ 存储桶操作失败: ${error}`);
        return false;
    }
    // 3. 测试文件上传
    console.log('\n3. 测试文件上传:');
    try {
        const testFileName = `test-${Date.now()}.txt`;
        const testContent = '这是一个测试文件内容';
        const testBuffer = Buffer.from(testContent, 'utf8');
        console.log(`   📤 上传测试文件: ${testFileName}`);
        await minioClient.putObject(bucketName, testFileName, testBuffer, testBuffer.length, {
            'Content-Type': 'text/plain',
            'x-amz-meta-test': 'true'
        });
        console.log(`   ✅ 文件上传成功`);
        // 4. 测试文件下载
        console.log('\n4. 测试文件下载:');
        const downloadStream = await minioClient.getObject(bucketName, testFileName);
        let downloadedContent = '';
        downloadStream.on('data', (chunk) => {
            downloadedContent += chunk.toString();
        });
        await new Promise((resolve, reject) => {
            downloadStream.on('end', resolve);
            downloadStream.on('error', reject);
        });
        if (downloadedContent === testContent) {
            console.log(`   ✅ 文件下载成功，内容一致`);
        }
        else {
            console.log(`   ❌ 文件下载失败，内容不一致`);
            return false;
        }
        // 5. 清理测试文件
        console.log('\n5. 清理测试文件:');
        await minioClient.removeObject(bucketName, testFileName);
        console.log(`   ✅ 测试文件清理完成`);
    }
    catch (error) {
        console.log(`   ❌ 文件操作测试失败: ${error}`);
        return false;
    }
    console.log('\n🎉 MinIO存储服务修复完成！');
    return true;
}
// 运行修复
fixUploadIssue().then(success => {
    if (success) {
        console.log('\n✅ 文件上传功能应该现在可以正常工作了');
        console.log('\n接下来请测试:');
        console.log('1. 重启后端服务');
        console.log('2. 尝试通过前端上传文件');
        console.log('3. 如果仍有问题，运行诊断脚本: npm run diagnose-upload');
    }
    else {
        console.log('\n❌ 修复失败，请检查上述错误信息');
        process.exit(1);
    }
}).catch(error => {
    console.error('修复过程中发生错误:', error);
    process.exit(1);
});
//# sourceMappingURL=fix-upload-issue.js.map