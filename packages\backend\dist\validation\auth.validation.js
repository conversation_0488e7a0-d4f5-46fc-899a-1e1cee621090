"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authValidation = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * Authentication validation schemas
 */
exports.authValidation = {
    /**
     * Validation schema for user registration
     */
    register: joi_1.default.object({
        email: joi_1.default.string().email().required().messages({
            'string.email': 'Email must be a valid email address',
            'any.required': 'Email is required'
        }),
        username: joi_1.default.string()
            .min(3)
            .max(30)
            .pattern(/^[a-zA-Z0-9_]+$/)
            .required()
            .messages({
            'string.min': 'Username must be at least 3 characters long',
            'string.max': 'Username must be at most 30 characters long',
            'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
            'any.required': 'Username is required'
        }),
        password: joi_1.default.string()
            .min(8)
            .pattern(/[A-Z]/)
            .pattern(/[a-z]/)
            .pattern(/\d/)
            .required()
            .messages({
            'string.min': 'Password must be at least 8 characters long',
            'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
            'any.required': 'Password is required'
        })
    }),
    /**
     * Validation schema for user login
     */
    login: joi_1.default.object({
        email: joi_1.default.string().email().required().messages({
            'string.email': 'Email must be a valid email address',
            'any.required': 'Email is required'
        }),
        password: joi_1.default.string().required().messages({
            'any.required': 'Password is required'
        }),
        rememberMe: joi_1.default.boolean().default(false)
    }),
    /**
     * Validation schema for token refresh
     */
    refreshToken: joi_1.default.object({
        refreshToken: joi_1.default.string().messages({
            'any.required': 'Refresh token is required'
        })
    }).unknown(true) // Allow other fields for flexibility
};
//# sourceMappingURL=auth.validation.js.map