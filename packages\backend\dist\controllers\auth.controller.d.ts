import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
/**
 * Authentication Controller
 * Handles HTTP requests related to authentication
 */
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    /**
     * Register a new user
     * @param req Express request
     * @param res Express response
     */
    register: (req: Request, res: Response) => Promise<void>;
    /**
     * Login a user
     * @param req Express request
     * @param res Express response
     */
    login: (req: Request, res: Response) => Promise<void>;
    /**
     * Refresh access token
     * @param req Express request
     * @param res Express response
     */
    refreshToken: (req: Request, res: Response) => Promise<void>;
    /**
     * Logout user
     * @param req Express request
     * @param res Express response
     */
    logout: (req: Request, res: Response) => Promise<void>;
    /**
     * Get active sessions for current user
     * @param req Express request
     * @param res Express response
     */
    getActiveSessions: (req: Request, res: Response) => Promise<void>;
    /**
     * Revoke a session
     * @param req Express request
     * @param res Express response
     */
    revokeSession: (req: Request, res: Response) => Promise<void>;
    /**
     * Logout from all other sessions
     * @param req Express request
     * @param res Express response
     */
    logoutFromAllOtherSessions: (req: Request, res: Response) => Promise<void>;
    /**
     * Logout from all sessions
     * @param req Express request
     * @param res Express response
     */
    logoutFromAllSessions: (req: Request, res: Response) => Promise<void>;
    /**
     * Get current session info
     * @param req Express request
     * @param res Express response
     */
    getCurrentSession: (req: Request, res: Response) => Promise<void>;
    /**
     * Get device sessions with enhanced tracking
     * @param req Express request
     * @param res Express response
     */
    getDeviceSessions: (req: Request, res: Response) => Promise<void>;
    /**
     * Logout from specific device type
     * @param req Express request
     * @param res Express response
     */
    logoutFromDeviceType: (req: Request, res: Response) => Promise<void>;
    /**
     * Get session statistics
     * @param req Express request
     * @param res Express response
     */
    getSessionStatistics: (req: Request, res: Response) => Promise<void>;
    /**
     * Validate and cleanup sessions
     * @param req Express request
     * @param res Express response
     */
    validateAndCleanupSessions: (req: Request, res: Response) => Promise<void>;
    /**
     * Force security sync across all devices
     * @param req Express request
     * @param res Express response
     */
    forceSecuritySync: (req: Request, res: Response) => Promise<void>;
    /**
     * Enable two-factor authentication
     * @param req Express request
     * @param res Express response
     */
    enableTwoFactor: (req: Request, res: Response) => Promise<void>;
    /**
     * Verify and complete two-factor authentication setup
     * @param req Express request
     * @param res Express response
     */
    verifyAndEnableTwoFactor: (req: Request, res: Response) => Promise<void>;
    /**
     * Disable two-factor authentication
     * @param req Express request
     * @param res Express response
     */
    disableTwoFactor: (req: Request, res: Response) => Promise<void>;
    /**
     * Get two-factor authentication status
     * @param req Express request
     * @param res Express response
     */
    getTwoFactorStatus: (req: Request, res: Response) => Promise<void>;
    /**
     * Regenerate backup codes
     * @param req Express request
     * @param res Express response
     */
    regenerateBackupCodes: (req: Request, res: Response) => Promise<void>;
    /**
     * Get current user profile
     * @param req Express request
     * @param res Express response
     */
    getUserProfile: (req: Request, res: Response) => Promise<void>;
}
//# sourceMappingURL=auth.controller.d.ts.map