"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.request = request;
exports.requestWithRetry = requestWithRetry;
var _stream = require("stream");
var _util = require("util");
const pipelineAsync = (0, _util.promisify)(_stream.pipeline);
async function request(transport, opt, body = null) {
  return new Promise((resolve, reject) => {
    const requestObj = transport.request(opt, response => {
      resolve(response);
    });
    requestObj.on('error', reject);
    if (!body || Buffer.isBuffer(body) || typeof body === 'string') {
      requestObj.end(body);
    } else {
      pipelineAsync(body, requestObj).catch(reject);
    }
  });
}
const MAX_RETRIES = 10;
const EXP_BACK_OFF_BASE_DELAY = 1000; // Base delay for exponential backoff
const ADDITIONAL_DELAY_FACTOR = 1.0; // to avoid synchronized retries

// Retryable error codes for HTTP ( ref: minio-go)
const retryHttpCodes = {
  408: true,
  429: true,
  499: true,
  500: true,
  502: true,
  503: true,
  504: true,
  520: true
};
exports.retryHttpCodes = retryHttpCodes;
const isHttpRetryable = httpResCode => {
  return retryHttpCodes[httpResCode] !== undefined;
};
const sleep = ms => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
const getExpBackOffDelay = retryCount => {
  const backOffBy = EXP_BACK_OFF_BASE_DELAY * 2 ** retryCount;
  const additionalDelay = Math.random() * backOffBy * ADDITIONAL_DELAY_FACTOR;
  return backOffBy + additionalDelay;
};
async function requestWithRetry(transport, opt, body = null, maxRetries = MAX_RETRIES) {
  let attempt = 0;
  let isRetryable = false;
  while (attempt <= maxRetries) {
    try {
      const response = await request(transport, opt, body);
      // Check if the HTTP status code is retryable
      if (isHttpRetryable(response.statusCode)) {
        isRetryable = true;
        throw new Error(`Retryable HTTP status: ${response.statusCode}`); // trigger retry attempt with calculated delay
      }

      return response; // Success, return the raw response
    } catch (err) {
      if (isRetryable) {
        attempt++;
        isRetryable = false;
        if (attempt > maxRetries) {
          throw new Error(`Request failed after ${maxRetries} retries: ${err}`);
        }
        const delay = getExpBackOffDelay(attempt);
        // eslint-disable-next-line no-console
        console.warn(`${new Date().toLocaleString()} Retrying request (attempt ${attempt}/${maxRetries}) after ${delay}ms due to: ${err}`);
        await sleep(delay);
      } else {
        throw err; // re-throw if any request, syntax errors
      }
    }
  }

  throw new Error(`${MAX_RETRIES} Retries exhausted, request failed.`);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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