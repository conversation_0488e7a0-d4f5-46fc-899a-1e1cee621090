import { RedisClientType } from 'redis';
export declare function connectRedis(): Promise<void>;
export declare function getRedisClient(): RedisClientType;
export declare function closeRedis(): Promise<void>;
export declare class CacheService {
    private static client;
    static initialize(client: RedisClientType): void;
    static get(key: string): Promise<string | null>;
    static set(key: string, value: string, ttlSeconds?: number): Promise<boolean>;
    static del(key: string): Promise<boolean>;
    static exists(key: string): Promise<boolean>;
}
//# sourceMappingURL=redis.d.ts.map