{"version": 3, "file": "mongodb.js", "sourceRoot": "", "sources": ["../../src/config/mongodb.ts"], "names": [], "mappings": ";;;AAkOA,oDAOC;AAzOD,qCAAqD;AAOrD,MAAa,OAAO;IAKlB,YAAY,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QACzC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,MAAoB;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAA;YACrE,CAAC;YACD,OAAO,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,CAAA;QACxC,CAAC;QACD,OAAO,OAAO,CAAC,QAAQ,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;YAC3B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YAClD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAA;YAClD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;YACvD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,EAAE,CAAA;IAChB,CAAC;IAED,aAAa,CAAU,IAAY;QACjC,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU,CAAI,IAAI,CAAC,CAAA;IACpC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;IAC3B,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAA;YAClE,MAAM,sBAAsB,CAAC,aAAa,CAAC;gBACzC,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,0BAA0B,EAAE;gBACxD,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gBAC5D,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gBAC5D,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gBAC5D,EAAE,GAAG,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,8BAA8B,EAAE;gBACjE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBACpD,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,mCAAmC,EAAE;gBAC9E,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,wCAAwC,EAAE;aACzF,CAAC,CAAA;YAEF,iBAAiB;YACjB,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YACtD,MAAM,gBAAgB,CAAC,aAAa,CAAC;gBACnC,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE;gBAClD,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,sBAAsB,EAAE;gBACtD,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE;gBAC9C,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,6BAA6B,EAAE;gBACxE;oBACE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;oBACxC,MAAM,EAAE,IAAI;oBACZ,IAAI,EAAE,oCAAoC;oBAC1C,uBAAuB,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;iBACrD;aACF,CAAC,CAAA;YAEF,qBAAqB;YACrB,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;YAC7D,MAAM,mBAAmB,CAAC,aAAa,CAAC;gBACtC,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,8BAA8B,EAAE;gBACzE,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBACtD,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBACtD,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,2BAA2B,EAAE;gBAC7D;oBACE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;oBACrB,IAAI,EAAE,+BAA+B;oBACrC,kBAAkB,EAAE,CAAC;oBACrB,uBAAuB,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;iBAC1D;aACF,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;YACzD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAA;YAClE,MAAM,sBAAsB,CAAC,WAAW,CACtC,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAC1B;gBACE,IAAI,EAAE,0CAA0C;gBAChD,MAAM,EAAE,IAAI;aACb,CACF,CAAA;YAED,4CAA4C;YAC5C,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;gBACpB,OAAO,EAAE,eAAe;gBACxB,SAAS,EAAE;oBACT,WAAW,EAAE;wBACX,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,CAAC;wBAC5G,UAAU,EAAE;4BACV,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC5C,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC9C,YAAY,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAClD,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC9C,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;4BACxC,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC9C,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;4BAC1C,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;4BAC9B,UAAU,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;4BAChC,UAAU,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;4BAChC,IAAI,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE;4BAC1D,YAAY,EAAE;gCACZ,QAAQ,EAAE,OAAO;gCACjB,KAAK,EAAE;oCACL,QAAQ,EAAE,QAAQ;oCAClB,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC;oCAC9C,UAAU,EAAE;wCACV,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;wCAC1B,GAAG,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;wCAC3B,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;wCAC9B,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;qCAChC;iCACF;6BACF;yBACF;qBACF;iBACF;gBACD,eAAe,EAAE,UAAU;gBAC3B,gBAAgB,EAAE,MAAM;aACzB,CAAC,CAAA;YAEF,sCAAsC;YACtC,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;gBACpB,OAAO,EAAE,SAAS;gBAClB,SAAS,EAAE;oBACT,WAAW,EAAE;wBACX,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC;wBAC/D,UAAU,EAAE;4BACV,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC5C,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC1C,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;4BAC1C,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC1C,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;4BAC/B,UAAU,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;yBACjC;qBACF;iBACF;gBACD,eAAe,EAAE,UAAU;gBAC3B,gBAAgB,EAAE,MAAM;aACzB,CAAC,CAAA;YAEF,0CAA0C;YAC1C,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;gBACpB,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE;oBACT,WAAW,EAAE;wBACX,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,CAAC;wBACpF,UAAU,EAAE;4BACV,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC5C,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC5C,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;4BAC3C,WAAW,EAAE;gCACX,QAAQ,EAAE,OAAO;gCACjB,KAAK,EAAE;oCACL,QAAQ,EAAE,QAAQ;oCAClB,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;oCAC7B,UAAU,EAAE;wCACV,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;wCAC3C,OAAO,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;qCAC9B;iCACF;6BACF;4BACD,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;4BACzC,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;4BAC1C,aAAa,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;4BACjD,YAAY,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;4BAC1D,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;yBAChC;qBACF;iBACF;gBACD,eAAe,EAAE,UAAU;gBAC3B,gBAAgB,EAAE,MAAM;aACzB,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAA;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;YACjE,qDAAqD;QACvD,CAAC;IACH,CAAC;CACF;AAxND,0BAwNC;AAED,yEAAyE;AACzE,SAAgB,oBAAoB;IAClC,MAAM,MAAM,GAAgB;QAC1B,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,2BAA2B;QAC3D,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,eAAe;KACpD,CAAA;IAED,OAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;AACpC,CAAC"}