{"name": "block-stream2", "version": "2.1.0", "description": "transform input into equally-sized blocks of output", "main": "index.js", "dependencies": {"readable-stream": "^3.4.0"}, "devDependencies": {"standard": "^16.0.3", "tape": "^4.2.2"}, "scripts": {"test": "standard && tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/block-stream2.git"}, "homepage": "https://github.com/substack/block-stream2", "keywords": ["stream", "block", "chunk", "size", "streams2", "streams3"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}