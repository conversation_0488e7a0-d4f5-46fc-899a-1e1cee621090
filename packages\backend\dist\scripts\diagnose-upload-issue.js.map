{"version": 3, "file": "diagnose-upload-issue.js", "sourceRoot": "", "sources": ["../../src/scripts/diagnose-upload-issue.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,iEAA6D;AAC7D,2DAAuD;AAEvD,+CAAiC;AAEjC,KAAK,UAAU,mBAAmB;IAChC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAElC,gBAAgB;IAChB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,gCAAc,CAAC,eAAe,EAAE,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,gBAAgB,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAChF,OAAO;QACT,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;QACvC,OAAO;IACT,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,MAAM,gCAAc,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE;YAChF,YAAY,EAAE,UAAU;YACxB,QAAQ,EAAE,YAAY;SACvB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,CAAC,WAAW,aAAa,UAAU,CAAC,SAAS,KAAK,CAAC,CAAC;QAEzF,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,gBAAgB,GAAG,MAAM,gCAAc,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAE9E,IAAI,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACnC,CAAC;QAED,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,IAAI,CAAC;YACH,MAAM,gCAAc,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;QAC3C,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;QAErC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;YAClF,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC9C,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QACD,OAAO;IACT,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,CAAC;QACH,SAAS;QACT,MAAM,UAAU,GAAG,YAAY,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACtD,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC3B,QAAQ,EAAE,iBAAiB;YAC3B,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,MAAM,CAAC;SACf,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,0BAAW,CAAC,UAAU,CAAC,UAAU,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC;IAExE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;QAEvC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,6EAA6E,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC;QACD,OAAO;IACT,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IACzC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAC/B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC7B,CAAC;AAED,OAAO;AACP,mBAAmB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAClC,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}