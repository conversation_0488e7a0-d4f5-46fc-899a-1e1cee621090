import { FileMetadata, FileUpload, FileInfo, FileList, SearchQuery, Pagination, FolderData, Folder, ShareLink, ShareOptions } from '@cloud-storage/shared';
export interface FileStream {
    stream: NodeJS.ReadableStream;
    metadata: FileMetadata;
}
export interface ChunkedUploadInit {
    filename: string;
    fileSize: number;
    mimeType: string;
    totalChunks: number;
    folderId?: string;
    tags?: string[];
}
export interface ChunkUpload {
    chunkIndex: number;
    chunkData: Buffer;
    chunkSize: number;
}
export interface ChunkUploadResult {
    chunkIndex: number;
    uploaded: number;
    total: number;
    progress: number;
}
export interface UploadProgress {
    uploadId: string;
    filename: string;
    totalChunks: number;
    uploadedChunks: number;
    progress: number;
    status: 'pending' | 'uploading' | 'completed' | 'failed';
    createdAt: Date;
    updatedAt: Date;
}
export declare class FileService {
    uploadFile(userId: string, fileUpload: FileUpload, metadata: Partial<FileMetadata>): Promise<FileInfo>;
    uploadFiles(userId: string, files: FileUpload[]): Promise<FileInfo[]>;
    downloadFile(fileId: string, userId: string): Promise<FileStream>;
    getFilePreview(fileId: string, userId: string, options?: {
        quality?: string;
        maxWidth?: number;
        maxHeight?: number;
    }): Promise<FileStream>;
    private resizeImage;
    listFiles(userId: string, folderId?: string, pagination?: Pagination): Promise<FileList>;
    searchFiles(userId: string, query: SearchQuery): Promise<FileList>;
    createFolder(userId: string, folderData: FolderData): Promise<Folder>;
    moveFile(fileId: string, targetFolderId: string, userId: string): Promise<void>;
    deleteFile(fileId: string, userId: string): Promise<void>;
    shareFile(fileId: string, userId: string, shareOptions: ShareOptions): Promise<ShareLink>;
    private generateUniqueFilename;
    private extractStorageNodes;
    private generateDownloadUrl;
    private buildFolderPath;
    getStorageStats(userId: string): Promise<{
        totalFiles: number;
        totalSize: number;
        storageQuota: number;
        usedPercentage: number;
    }>;
    initChunkedUpload(userId: string, uploadInit: ChunkedUploadInit): Promise<string>;
    uploadChunk(uploadId: string, chunk: ChunkUpload): Promise<ChunkUploadResult>;
    completeChunkedUpload(uploadId: string, userId: string): Promise<FileInfo>;
    getUploadProgress(uploadId: string, userId: string): Promise<UploadProgress>;
    cleanupExpiredUploads(): Promise<void>;
    getFileInfo(fileId: string, userId: string): Promise<FileMetadata>;
    renameFolder(folderId: string, newName: string, userId: string): Promise<void>;
    moveFolder(folderId: string, targetFolderId: string, userId: string): Promise<void>;
    deleteFolderToTrash(folderId: string, userId: string): Promise<void>;
    deleteFolderPermanently(folderId: string, userId: string): Promise<void>;
    renameFile(fileId: string, newName: string, userId: string): Promise<void>;
    moveFileToTrash(fileId: string, userId: string): Promise<void>;
    getTrashContents(userId: string, pagination: Pagination): Promise<FileList>;
    restoreFromTrash(fileId: string, userId: string): Promise<void>;
    emptyTrash(userId: string): Promise<number>;
    private isCircularReference;
    private markFolderContentsAsDeleted;
    private deleteFolderContentsPermanently;
    getFileShares(fileId: string, userId: string): Promise<ShareLink[]>;
    updateShareLink(shareId: string, userId: string, updates: Partial<ShareOptions>): Promise<void>;
    revokeShareLink(shareId: string, userId: string): Promise<void>;
    getShareStats(userId: string): Promise<{
        totalShares: number;
        activeShares: number;
        totalDownloads: number;
    }>;
    getPublicShareInfo(token: string, password?: string): Promise<{
        fileInfo: {
            id: string;
            filename: string;
            size: number;
            mimeType: string;
            uploadedAt: Date;
        };
        shareInfo: {
            downloadCount: number;
            maxDownloads?: number;
            expiresAt?: Date;
            requiresPassword: boolean;
        };
    }>;
    downloadViaPublicShare(token: string, password?: string): Promise<FileStream>;
    cleanupExpiredShares(): Promise<number>;
}
export declare const fileService: FileService;
export default fileService;
//# sourceMappingURL=file.service.d.ts.map