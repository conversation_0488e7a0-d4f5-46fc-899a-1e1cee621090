import { Client as MinioClient, BucketItem } from 'minio';
import { Readable } from 'stream';
import * as crypto from 'crypto';
import { FileMetadata, StorageNode, FileUpload, FileInfo } from '@cloud-storage/shared';
import config from '../config';
import logger from '../utils/logger';

export interface StorageConfig {
  nodes: StorageNodeConfig[];
  defaultBucket: string;
  chunkSize: number;
  replicationFactor: number;
}

export interface StorageNodeConfig {
  id: string;
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  region: string;
  isHealthy: boolean;
}

export interface ChunkInfo {
  id: string;
  index: number;
  size: number;
  checksum: string;
  storageNodes: string[];
}

export interface FileChunks {
  fileId: string;
  totalChunks: number;
  chunks: ChunkInfo[];
  totalSize: number;
  checksum: string;
}

export class StorageService {
  public clients: Map<string, MinioClient> = new Map();
  private storageNodes: Map<string, StorageNodeConfig> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(private config: StorageConfig) {
    this.initializeClients();
    // this.startHealthCheck();
  }

  private initializeClients(): void {
    for (const node of this.config.nodes) {
      try {
        const client = new MinioClient({
          endPoint: node.endPoint,
          port: node.port,
          useSSL: node.useSSL,
          accessKey: node.accessKey,
          secretKey: node.secretKey,
          region: node.region
        });

        this.clients.set(node.id, client);
        this.storageNodes.set(node.id, node);
        
        logger.info(`Initialized storage client for node: ${node.id}`);
      } catch (error) {
        logger.error(`Failed to initialize storage client for node ${node.id}:`, error);
        this.storageNodes.set(node.id, { ...node, isHealthy: false });
      }
    }
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  private async performHealthCheck(): Promise<void> {
    for (const [nodeId, client] of Array.from(this.clients.entries())) {
      try {
        // 使用更简单的健康检查方法
        await client.listBuckets();
        const node = this.storageNodes.get(nodeId);
        if (node) {
          node.isHealthy = true;
          this.storageNodes.set(nodeId, node);
          logger.debug(`Storage node ${nodeId} is healthy`);
        }
      } catch (error) {
        logger.warn(`Health check failed for storage node ${nodeId}:`, error);
        const node = this.storageNodes.get(nodeId);
        if (node) {
          node.isHealthy = false;
          this.storageNodes.set(nodeId, node);
        }
      }
    }
  }

  public getHealthyNodes(): StorageNodeConfig[] {
    return Array.from(this.storageNodes.values()).filter(node => node.isHealthy);
  }

  public selectStorageNodes(count: number = this.config.replicationFactor): StorageNodeConfig[] {
    const healthyNodes = this.getHealthyNodes();
    
    if (healthyNodes.length < count) {
      logger.warn(`Only ${healthyNodes.length} healthy nodes available, requested ${count}`);
    }

    // Simple round-robin selection for now
    // In production, you might want more sophisticated load balancing
    const selectedNodes: StorageNodeConfig[] = [];
    for (let i = 0; i < Math.min(count, healthyNodes.length); i++) {
      selectedNodes.push(healthyNodes[i % healthyNodes.length]);
    }

    return selectedNodes;
  }

  private generateChunkId(fileId: string, chunkIndex: number): string {
    return `${fileId}_chunk_${chunkIndex}`;
  }

  private calculateChecksum(data: Buffer): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private async ensureBucketExists(client: MinioClient, bucketName: string): Promise<void> {
    const exists = await client.bucketExists(bucketName);
    if (!exists) {
      await client.makeBucket(bucketName);
      logger.info(`Created bucket: ${bucketName}`);
    }
  }

  public async uploadFileChunked(
    fileId: string,
    fileBuffer: Buffer,
    metadata: Partial<FileMetadata>
  ): Promise<FileChunks> {
    const totalSize = fileBuffer.length;
    const chunkSize = this.config.chunkSize;
    const totalChunks = Math.ceil(totalSize / chunkSize);
    const chunks: ChunkInfo[] = [];
    const fileChecksum = this.calculateChecksum(fileBuffer);

    logger.info(`Starting chunked upload for file ${fileId}, ${totalChunks} chunks`);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, totalSize);
      const chunkBuffer = fileBuffer.slice(start, end);
      const chunkId = this.generateChunkId(fileId, i);
      const chunkChecksum = this.calculateChecksum(chunkBuffer);

      // Select storage nodes for this chunk
      const selectedNodes = this.selectStorageNodes();
      const uploadedNodes: string[] = [];

      // Upload chunk to selected nodes
      for (const node of selectedNodes) {
        try {
          const client = this.clients.get(node.id);
          if (!client) continue;

          await this.ensureBucketExists(client, this.config.defaultBucket);
          
          const stream = Readable.from(chunkBuffer);
          await client.putObject(
            this.config.defaultBucket,
            chunkId,
            stream,
            chunkBuffer.length,
            {
              'Content-Type': 'application/octet-stream',
              'x-amz-meta-file-id': fileId,
              'x-amz-meta-chunk-index': i.toString(),
              'x-amz-meta-checksum': chunkChecksum,
              'x-amz-meta-original-name': metadata.originalName || '',
              'x-amz-meta-mime-type': metadata.mimeType || 'application/octet-stream'
            }
          );

          uploadedNodes.push(node.id);
          logger.debug(`Uploaded chunk ${chunkId} to node ${node.id}`);
        } catch (error) {
          logger.error(`Failed to upload chunk ${chunkId} to node ${node.id}:`, error);
        }
      }

      if (uploadedNodes.length === 0) {
        throw new Error(`Failed to upload chunk ${chunkId} to any storage node`);
      }

      chunks.push({
        id: chunkId,
        index: i,
        size: chunkBuffer.length,
        checksum: chunkChecksum,
        storageNodes: uploadedNodes
      });
    }

    const fileChunks: FileChunks = {
      fileId,
      totalChunks,
      chunks,
      totalSize,
      checksum: fileChecksum
    };

    logger.info(`Completed chunked upload for file ${fileId}`);
    return fileChunks;
  }

  public async downloadFileChunked(fileChunks: FileChunks): Promise<Buffer> {
    const chunks: Buffer[] = new Array(fileChunks.totalChunks);
    
    logger.info(`Starting chunked download for file ${fileChunks.fileId}`);

    // Download chunks in parallel
    const downloadPromises = fileChunks.chunks.map(async (chunkInfo) => {
      let chunkBuffer: Buffer | null = null;
      
      // Try to download from available nodes
      for (const nodeId of chunkInfo.storageNodes) {
        try {
          const client = this.clients.get(nodeId);
          if (!client) continue;

          const stream = await client.getObject(this.config.defaultBucket, chunkInfo.id);
          const buffers: Buffer[] = [];
          
          for await (const chunk of stream) {
            buffers.push(chunk);
          }
          
          chunkBuffer = Buffer.concat(buffers);
          
          // Verify chunk integrity
          const downloadedChecksum = this.calculateChecksum(chunkBuffer);
          if (downloadedChecksum !== chunkInfo.checksum) {
            logger.warn(`Checksum mismatch for chunk ${chunkInfo.id} from node ${nodeId}`);
            chunkBuffer = null;
            continue;
          }
          
          logger.debug(`Downloaded chunk ${chunkInfo.id} from node ${nodeId}`);
          break;
        } catch (error) {
          logger.error(`Failed to download chunk ${chunkInfo.id} from node ${nodeId}:`, error);
        }
      }

      if (!chunkBuffer) {
        throw new Error(`Failed to download chunk ${chunkInfo.id} from any storage node`);
      }

      chunks[chunkInfo.index] = chunkBuffer;
    });

    await Promise.all(downloadPromises);

    const fileBuffer = Buffer.concat(chunks);
    
    // Verify file integrity
    const downloadedChecksum = this.calculateChecksum(fileBuffer);
    if (downloadedChecksum !== fileChunks.checksum) {
      throw new Error(`File integrity check failed for ${fileChunks.fileId}`);
    }

    logger.info(`Completed chunked download for file ${fileChunks.fileId}`);
    return fileBuffer;
  }

  public async deleteFileChunked(fileChunks: FileChunks): Promise<void> {
    logger.info(`Starting chunked deletion for file ${fileChunks.fileId}`);

    const deletePromises = fileChunks.chunks.map(async (chunkInfo) => {
      const nodePromises = chunkInfo.storageNodes.map(async (nodeId) => {
        try {
          const client = this.clients.get(nodeId);
          if (!client) return;

          await client.removeObject(this.config.defaultBucket, chunkInfo.id);
          logger.debug(`Deleted chunk ${chunkInfo.id} from node ${nodeId}`);
        } catch (error) {
          logger.error(`Failed to delete chunk ${chunkInfo.id} from node ${nodeId}:`, error);
        }
      });

      await Promise.all(nodePromises);
    });

    await Promise.all(deletePromises);
    logger.info(`Completed chunked deletion for file ${fileChunks.fileId}`);
  }

  public async getStorageStats(): Promise<{
    totalNodes: number;
    healthyNodes: number;
    totalSpace: number;
    usedSpace: number;
  }> {
    const healthyNodes = this.getHealthyNodes();
    let totalSpace = 0;
    let usedSpace = 0;

    // This is a simplified implementation
    // In a real scenario, you'd query each node for actual storage statistics
    for (const node of healthyNodes) {
      try {
        const client = this.clients.get(node.id);
        if (!client) continue;

        // For MinIO, we can't easily get storage stats without admin API
        // This is a placeholder implementation
        totalSpace += 1000000000000; // 1TB per node (placeholder)
        usedSpace += 100000000000;   // 100GB used per node (placeholder)
      } catch (error) {
        logger.error(`Failed to get stats for node ${node.id}:`, error);
      }
    }

    return {
      totalNodes: this.storageNodes.size,
      healthyNodes: healthyNodes.length,
      totalSpace,
      usedSpace
    };
  }

  public async listFiles(prefix?: string): Promise<BucketItem[]> {
    const healthyNodes = this.getHealthyNodes();
    if (healthyNodes.length === 0) {
      throw new Error('No healthy storage nodes available');
    }

    // Use the first healthy node to list files
    const client = this.clients.get(healthyNodes[0].id);
    if (!client) {
      throw new Error('No available storage client');
    }

    const objects: BucketItem[] = [];
    const stream = client.listObjects(this.config.defaultBucket, prefix, true);

    return new Promise((resolve, reject) => {
      stream.on('data', (obj) => {
        if (obj.name) {
          objects.push({
            name: obj.name,
            size: obj.size || 0,
            etag: obj.etag || '',
            lastModified: obj.lastModified || new Date()
          });
        }
      });
      stream.on('error', reject);
      stream.on('end', () => resolve(objects));
    });
  }

  public getConfig(): StorageConfig {
    return this.config;
  }

  public getClient(nodeId: string): MinioClient | undefined {
    return this.clients.get(nodeId);
  }

  public destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    this.clients.clear();
    this.storageNodes.clear();
    logger.info('Storage service destroyed');
  }
}

// Default storage configuration
const defaultStorageConfig: StorageConfig = {
  nodes: [
    {
      id: 'node-1',
      endPoint: process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(process.env.MINIO_PORT || '9000', 10),
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
      secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
      region: process.env.MINIO_REGION || 'us-east-1',
      isHealthy: true
    }
  ],
  defaultBucket: process.env.MINIO_BUCKET || 'cloud-storage',
  chunkSize: parseInt(process.env.CHUNK_SIZE || '5242880', 10), // 5MB chunks
  replicationFactor: parseInt(process.env.REPLICATION_FACTOR || '2', 10)
};

// Export singleton instance
export const storageService = new StorageService(defaultStorageConfig);
export default storageService;