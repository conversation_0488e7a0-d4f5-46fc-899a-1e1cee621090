"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const device_1 = require("../utils/device");
/**
 * Authentication Controller
 * Handles HTTP requests related to authentication
 */
class AuthController {
    constructor(authService) {
        /**
         * Register a new user
         * @param req Express request
         * @param res Express response
         */
        this.register = async (req, res) => {
            try {
                const userData = req.body;
                const deviceInfo = (0, device_1.extractDeviceInfo)(req);
                const ipAddress = req.ip || '0.0.0.0';
                const result = await this.authService.register(userData);
                // Set refresh token as HTTP-only cookie for auto-login
                if (result.tokens) {
                    res.cookie('refreshToken', result.tokens.refreshToken, {
                        httpOnly: true,
                        secure: process.env.NODE_ENV === 'production',
                        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                        path: '/api/auth/refresh-token',
                        sameSite: 'strict'
                    });
                }
                res.status(201).json(result);
            }
            catch (error) {
                // Enhance error response with field-specific validation errors
                if (error.name === 'ValidationError') {
                    const details = {
                        message: error.message,
                        errors: error.details?.map((err) => ({
                            field: err.path?.[0],
                            message: err.message
                        }))
                    };
                    res.status(400).json({
                        error: {
                            message: 'Validation failed',
                            code: 'VALIDATION_ERROR',
                            details
                        }
                    });
                }
                else if (error.code === 11000) {
                    // Handle MongoDB duplicate key error
                    const field = Object.keys(error.keyPattern)[0];
                    const errorMessage = field === 'email'
                        ? 'User with this email already exists'
                        : 'Username already exists';
                    res.status(400).json({
                        error: {
                            message: errorMessage,
                            code: field === 'email' ? 'EMAIL_ALREADY_EXISTS' : 'USERNAME_ALREADY_EXISTS'
                        }
                    });
                }
                else {
                    res.status(400).json({
                        error: {
                            message: error.message,
                            code: 'REGISTRATION_ERROR'
                        }
                    });
                }
            }
        };
        /**
         * Login a user
         * @param req Express request
         * @param res Express response
         */
        this.login = async (req, res) => {
            try {
                const credentials = req.body;
                const { twoFactorCode } = req.body;
                const deviceInfo = (0, device_1.extractDeviceInfo)(req);
                const ipAddress = req.ip || '0.0.0.0';
                const result = await this.authService.login(credentials, deviceInfo, ipAddress, twoFactorCode);
                // Set refresh token as HTTP-only cookie with appropriate expiration
                const cookieMaxAge = credentials.rememberMe
                    ? 30 * 24 * 60 * 60 * 1000 // 30 days for remember me
                    : 7 * 24 * 60 * 60 * 1000; // 7 days normal
                res.cookie('refreshToken', result.tokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    maxAge: cookieMaxAge,
                    path: '/api/auth/refresh-token',
                    sameSite: 'strict'
                });
                res.status(200).json(result);
            }
            catch (error) {
                // Handle specific 2FA errors
                if (error.message === 'Two-factor authentication code required') {
                    res.status(200).json({
                        requiresTwoFactor: true,
                        message: 'Two-factor authentication code required'
                    });
                    return;
                }
                res.status(401).json({ error: error.message });
            }
        };
        /**
         * Refresh access token
         * @param req Express request
         * @param res Express response
         */
        this.refreshToken = async (req, res) => {
            try {
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!refreshToken) {
                    res.status(401).json({ error: 'Refresh token is required' });
                    return;
                }
                const tokens = await this.authService.refreshToken(refreshToken);
                // Update refresh token cookie
                res.cookie('refreshToken', tokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                    path: '/api/auth/refresh-token',
                });
                res.status(200).json(tokens);
            }
            catch (error) {
                res.status(401).json({ error: error.message });
            }
        };
        /**
         * Logout user
         * @param req Express request
         * @param res Express response
         */
        this.logout = async (req, res) => {
            try {
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (refreshToken) {
                    await this.authService.logout(refreshToken);
                }
                // Clear refresh token cookie
                res.clearCookie('refreshToken', {
                    path: '/api/auth/refresh-token',
                });
                res.status(200).json({ message: 'Logged out successfully' });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Get active sessions for current user
         * @param req Express request
         * @param res Express response
         */
        this.getActiveSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const sessions = await this.authService.getActiveSessions(userId);
                res.status(200).json(sessions);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Revoke a session
         * @param req Express request
         * @param res Express response
         */
        this.revokeSession = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { sessionId } = req.params;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                // Verify session belongs to user
                const sessions = await this.authService.getActiveSessions(userId);
                const sessionExists = sessions.some(session => session.id === sessionId);
                if (!sessionExists) {
                    res.status(404).json({ error: 'Session not found' });
                    return;
                }
                await this.authService.revokeSession(sessionId);
                res.status(200).json({ message: 'Session revoked successfully' });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Logout from all other sessions
         * @param req Express request
         * @param res Express response
         */
        this.logoutFromAllOtherSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                // Get current session ID
                let currentSessionId;
                if (refreshToken) {
                    const currentSession = await this.authService.getSessionByRefreshToken(refreshToken);
                    currentSessionId = currentSession?.id;
                }
                const revokedCount = await this.authService.logoutFromAllOtherSessions(userId, currentSessionId);
                res.status(200).json({
                    message: 'Logged out from all other sessions successfully',
                    revokedSessions: revokedCount
                });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Logout from all sessions
         * @param req Express request
         * @param res Express response
         */
        this.logoutFromAllSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const revokedCount = await this.authService.logoutFromAllSessions(userId);
                // Clear refresh token cookie
                res.clearCookie('refreshToken', {
                    path: '/api/auth/refresh-token',
                });
                res.status(200).json({
                    message: 'Logged out from all sessions successfully',
                    revokedSessions: revokedCount
                });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Get current session info
         * @param req Express request
         * @param res Express response
         */
        this.getCurrentSession = async (req, res) => {
            try {
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!refreshToken) {
                    res.status(401).json({ error: 'No active session' });
                    return;
                }
                const session = await this.authService.getSessionByRefreshToken(refreshToken);
                if (!session) {
                    res.status(401).json({ error: 'Invalid session' });
                    return;
                }
                res.status(200).json(session);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Get device sessions with enhanced tracking
         * @param req Express request
         * @param res Express response
         */
        this.getDeviceSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                const includeInactive = req.query.includeInactive === 'true';
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const sessions = await this.authService.getDeviceSessions(userId, includeInactive);
                // Mark current session
                if (refreshToken) {
                    const currentSession = await this.authService.getSessionByRefreshToken(refreshToken);
                    if (currentSession) {
                        const sessionIndex = sessions.findIndex(s => s.id === currentSession.id);
                        if (sessionIndex >= 0) {
                            sessions[sessionIndex].isCurrent = true;
                        }
                    }
                }
                res.status(200).json(sessions);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Logout from specific device type
         * @param req Express request
         * @param res Express response
         */
        this.logoutFromDeviceType = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { deviceType } = req.params;
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                // Get current session ID to exclude
                let currentSessionId;
                if (refreshToken) {
                    const currentSession = await this.authService.getSessionByRefreshToken(refreshToken);
                    currentSessionId = currentSession?.id;
                }
                const revokedCount = await this.authService.logoutFromDeviceType(userId, deviceType, currentSessionId);
                // Synchronize status across devices
                await this.authService.synchronizeDeviceStatus(userId, 'logout', currentSessionId);
                res.status(200).json({
                    message: `Logged out from all ${deviceType} devices`,
                    revokedSessions: revokedCount
                });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Get session statistics
         * @param req Express request
         * @param res Express response
         */
        this.getSessionStatistics = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const statistics = await this.authService.getSessionStatistics(userId);
                res.status(200).json(statistics);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Validate and cleanup sessions
         * @param req Express request
         * @param res Express response
         */
        this.validateAndCleanupSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const results = await this.authService.validateAndCleanupSessions(userId);
                res.status(200).json({
                    message: 'Session cleanup completed',
                    results
                });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Force security sync across all devices
         * @param req Express request
         * @param res Express response
         */
        this.forceSecuritySync = async (req, res) => {
            try {
                const userId = req.user?.id;
                const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                // Get current session ID to exclude
                let currentSessionId;
                if (refreshToken) {
                    const currentSession = await this.authService.getSessionByRefreshToken(refreshToken);
                    currentSessionId = currentSession?.id;
                }
                // Force logout from all other sessions due to security change
                await this.authService.synchronizeDeviceStatus(userId, 'security_change', currentSessionId);
                res.status(200).json({
                    message: 'Security sync completed - all other sessions have been terminated'
                });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        // Two-Factor Authentication Methods
        /**
         * Enable two-factor authentication
         * @param req Express request
         * @param res Express response
         */
        this.enableTwoFactor = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const setup = await this.authService.enableTwoFactor(userId);
                res.status(200).json(setup);
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        };
        /**
         * Verify and complete two-factor authentication setup
         * @param req Express request
         * @param res Express response
         */
        this.verifyAndEnableTwoFactor = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { code } = req.body;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                if (!code) {
                    res.status(400).json({ error: 'Verification code is required' });
                    return;
                }
                const success = await this.authService.verifyAndEnableTwoFactor(userId, code);
                if (success) {
                    // Force security sync to logout other sessions
                    await this.authService.synchronizeDeviceStatus(userId, 'security_change');
                    res.status(200).json({
                        message: 'Two-factor authentication enabled successfully',
                        success: true
                    });
                }
                else {
                    res.status(400).json({ error: 'Failed to enable two-factor authentication' });
                }
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        };
        /**
         * Disable two-factor authentication
         * @param req Express request
         * @param res Express response
         */
        this.disableTwoFactor = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { code } = req.body;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                if (!code) {
                    res.status(400).json({ error: 'Verification code is required' });
                    return;
                }
                const success = await this.authService.disableTwoFactor(userId, code);
                if (success) {
                    res.status(200).json({
                        message: 'Two-factor authentication disabled successfully',
                        success: true
                    });
                }
                else {
                    res.status(400).json({ error: 'Failed to disable two-factor authentication' });
                }
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        };
        /**
         * Get two-factor authentication status
         * @param req Express request
         * @param res Express response
         */
        this.getTwoFactorStatus = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                const status = await this.authService.getTwoFactorStatus(userId);
                res.status(200).json(status);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        /**
         * Regenerate backup codes
         * @param req Express request
         * @param res Express response
         */
        this.regenerateBackupCodes = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { code } = req.body;
                if (!userId) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                if (!code) {
                    res.status(400).json({ error: 'Verification code is required' });
                    return;
                }
                const backupCodes = await this.authService.regenerateBackupCodes(userId, code);
                res.status(200).json({
                    message: 'Backup codes regenerated successfully',
                    backupCodes
                });
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        };
        /**
         * Get current user profile
         * @param req Express request
         * @param res Express response
         */
        this.getUserProfile = async (req, res) => {
            try {
                const user = req.user;
                if (!user) {
                    res.status(401).json({ error: 'Unauthorized' });
                    return;
                }
                // Get 2FA status
                const twoFactorStatus = await this.authService.getTwoFactorStatus(user.id);
                // Return user profile with 2FA status
                const userProfile = {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    twoFactorEnabled: twoFactorStatus.enabled,
                    createdAt: new Date(), // This would come from database in real implementation
                    lastLoginAt: new Date() // This would come from database in real implementation
                };
                res.status(200).json(userProfile);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        };
        this.authService = authService;
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map