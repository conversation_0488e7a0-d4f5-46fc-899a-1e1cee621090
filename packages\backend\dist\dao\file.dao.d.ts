import { MongoDB } from '../config/mongodb';
import { FileMetadata, Folder, ShareLink, FileList, SearchQuery, ShareOptions, FolderData, Pagination } from '@cloud-storage/shared';
export declare class FileDAO {
    private fileMetadataModel;
    private folderModel;
    private shareLinkModel;
    constructor(mongodb: MongoDB);
    createFile(fileMetadata: FileMetadata): Promise<FileMetadata>;
    getFileById(id: string): Promise<FileMetadata | null>;
    getFilesByUserId(userId: string, folderId?: string): Promise<FileMetadata[]>;
    getFilesByUser(userId: string, folderId?: string, pagination?: Pagination): Promise<{
        files: FileMetadata[];
        totalCount: number;
    }>;
    private filterFilesByType;
    private sortFiles;
    getFoldersByParent(userId: string, parentId: string): Promise<Folder[]>;
    getRootFolders(userId: string): Promise<Folder[]>;
    searchFiles(userId: string, query: SearchQuery): Promise<{
        files: FileMetadata[];
        totalCount: number;
    }>;
    updateFile(id: string, updates: Partial<Omit<FileMetadata, 'id'>>): Promise<boolean>;
    deleteFile(id: string): Promise<boolean>;
    getFileByChecksum(checksum: string): Promise<FileMetadata | null>;
    getUserStorageStats(userId: string): Promise<{
        totalSize: number;
        fileCount: number;
    }>;
    saveFileChunks(fileId: string, fileChunks: any): Promise<void>;
    getFileChunks(fileId: string): Promise<any | null>;
    deleteFileChunks(fileId: string): Promise<boolean>;
    createFolder(folder: Folder): Promise<Folder>;
    getFolderById(id: string): Promise<Folder | null>;
    getFoldersByUserId(userId: string, parentId?: string): Promise<Folder[]>;
    getFolderByPath(userId: string, path: string): Promise<Folder | null>;
    updateFolder(id: string, updates: Partial<Omit<Folder, 'id'>>): Promise<boolean>;
    deleteFolder(id: string): Promise<boolean>;
    moveFolder(id: string, newParentId: string | undefined): Promise<boolean>;
    renameFolder(id: string, newName: string): Promise<boolean>;
    validateFolderName(userId: string, parentId: string | undefined, name: string): Promise<boolean>;
    createShareLink(shareLink: ShareLink): Promise<ShareLink>;
    getShareLinkById(id: string): Promise<ShareLink | null>;
    getShareLinkByToken(token: string): Promise<ShareLink | null>;
    getShareLinksByFileId(fileId: string): Promise<ShareLink[]>;
    getShareLinksByUserId(userId: string): Promise<ShareLink[]>;
    updateShareLink(id: string, updates: Partial<Omit<ShareLink, 'id' | 'token' | 'createdAt'>>): Promise<boolean>;
    deleteShareLink(id: string): Promise<boolean>;
    validateShareLink(token: string, password?: string): Promise<{
        valid: boolean;
        shareLink?: ShareLink;
        reason?: string;
    }>;
    recordDownload(shareId: string): Promise<boolean>;
    getFileList(userId: string, folderId?: string, pagination?: Pagination): Promise<FileList>;
    createFolderWithValidation(userId: string, folderData: FolderData): Promise<Folder>;
    shareFileWithOptions(fileId: string, userId: string, options: ShareOptions): Promise<ShareLink>;
    cleanupExpiredShares(): Promise<number>;
    getUserShareStats(userId: string): Promise<{
        totalShares: number;
        activeShares: number;
        totalDownloads: number;
    }>;
    getDeletedFiles(userId: string, pagination: Pagination): Promise<{
        files: FileMetadata[];
        totalCount: number;
    }>;
    getDeletedFolders(userId: string): Promise<Folder[]>;
    restoreFile(id: string): Promise<boolean>;
    restoreFolder(id: string): Promise<boolean>;
}
export declare const fileDao: FileDAO;
//# sourceMappingURL=file.dao.d.ts.map