"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.storageService = exports.StorageService = void 0;
const minio_1 = require("minio");
const stream_1 = require("stream");
const crypto = __importStar(require("crypto"));
const logger_1 = __importDefault(require("../utils/logger"));
class StorageService {
    constructor(config) {
        this.config = config;
        this.clients = new Map();
        this.storageNodes = new Map();
        this.healthCheckInterval = null;
        this.initializeClients();
        // this.startHealthCheck();
    }
    initializeClients() {
        for (const node of this.config.nodes) {
            try {
                const client = new minio_1.Client({
                    endPoint: node.endPoint,
                    port: node.port,
                    useSSL: node.useSSL,
                    accessKey: node.accessKey,
                    secretKey: node.secretKey,
                    region: node.region
                });
                this.clients.set(node.id, client);
                this.storageNodes.set(node.id, node);
                logger_1.default.info(`Initialized storage client for node: ${node.id}`);
            }
            catch (error) {
                logger_1.default.error(`Failed to initialize storage client for node ${node.id}:`, error);
                this.storageNodes.set(node.id, { ...node, isHealthy: false });
            }
        }
    }
    startHealthCheck() {
        this.healthCheckInterval = setInterval(async () => {
            await this.performHealthCheck();
        }, 30000); // Check every 30 seconds
    }
    async performHealthCheck() {
        for (const [nodeId, client] of Array.from(this.clients.entries())) {
            try {
                // 使用更简单的健康检查方法
                await client.listBuckets();
                const node = this.storageNodes.get(nodeId);
                if (node) {
                    node.isHealthy = true;
                    this.storageNodes.set(nodeId, node);
                    logger_1.default.debug(`Storage node ${nodeId} is healthy`);
                }
            }
            catch (error) {
                logger_1.default.warn(`Health check failed for storage node ${nodeId}:`, error);
                const node = this.storageNodes.get(nodeId);
                if (node) {
                    node.isHealthy = false;
                    this.storageNodes.set(nodeId, node);
                }
            }
        }
    }
    getHealthyNodes() {
        return Array.from(this.storageNodes.values()).filter(node => node.isHealthy);
    }
    selectStorageNodes(count = this.config.replicationFactor) {
        const healthyNodes = this.getHealthyNodes();
        if (healthyNodes.length < count) {
            logger_1.default.warn(`Only ${healthyNodes.length} healthy nodes available, requested ${count}`);
        }
        // Simple round-robin selection for now
        // In production, you might want more sophisticated load balancing
        const selectedNodes = [];
        for (let i = 0; i < Math.min(count, healthyNodes.length); i++) {
            selectedNodes.push(healthyNodes[i % healthyNodes.length]);
        }
        return selectedNodes;
    }
    generateChunkId(fileId, chunkIndex) {
        return `${fileId}_chunk_${chunkIndex}`;
    }
    calculateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }
    async ensureBucketExists(client, bucketName) {
        const exists = await client.bucketExists(bucketName);
        if (!exists) {
            await client.makeBucket(bucketName);
            logger_1.default.info(`Created bucket: ${bucketName}`);
        }
    }
    async uploadFileChunked(fileId, fileBuffer, metadata) {
        const totalSize = fileBuffer.length;
        const chunkSize = this.config.chunkSize;
        const totalChunks = Math.ceil(totalSize / chunkSize);
        const chunks = [];
        const fileChecksum = this.calculateChecksum(fileBuffer);
        logger_1.default.info(`Starting chunked upload for file ${fileId}, ${totalChunks} chunks`);
        for (let i = 0; i < totalChunks; i++) {
            const start = i * chunkSize;
            const end = Math.min(start + chunkSize, totalSize);
            const chunkBuffer = fileBuffer.slice(start, end);
            const chunkId = this.generateChunkId(fileId, i);
            const chunkChecksum = this.calculateChecksum(chunkBuffer);
            // Select storage nodes for this chunk
            const selectedNodes = this.selectStorageNodes();
            const uploadedNodes = [];
            // Upload chunk to selected nodes
            for (const node of selectedNodes) {
                try {
                    const client = this.clients.get(node.id);
                    if (!client)
                        continue;
                    await this.ensureBucketExists(client, this.config.defaultBucket);
                    const stream = stream_1.Readable.from(chunkBuffer);
                    await client.putObject(this.config.defaultBucket, chunkId, stream, chunkBuffer.length, {
                        'Content-Type': 'application/octet-stream',
                        'x-amz-meta-file-id': fileId,
                        'x-amz-meta-chunk-index': i.toString(),
                        'x-amz-meta-checksum': chunkChecksum,
                        'x-amz-meta-original-name': metadata.originalName || '',
                        'x-amz-meta-mime-type': metadata.mimeType || 'application/octet-stream'
                    });
                    uploadedNodes.push(node.id);
                    logger_1.default.debug(`Uploaded chunk ${chunkId} to node ${node.id}`);
                }
                catch (error) {
                    logger_1.default.error(`Failed to upload chunk ${chunkId} to node ${node.id}:`, error);
                }
            }
            if (uploadedNodes.length === 0) {
                throw new Error(`Failed to upload chunk ${chunkId} to any storage node`);
            }
            chunks.push({
                id: chunkId,
                index: i,
                size: chunkBuffer.length,
                checksum: chunkChecksum,
                storageNodes: uploadedNodes
            });
        }
        const fileChunks = {
            fileId,
            totalChunks,
            chunks,
            totalSize,
            checksum: fileChecksum
        };
        logger_1.default.info(`Completed chunked upload for file ${fileId}`);
        return fileChunks;
    }
    async downloadFileChunked(fileChunks) {
        const chunks = new Array(fileChunks.totalChunks);
        logger_1.default.info(`Starting chunked download for file ${fileChunks.fileId}`);
        // Download chunks in parallel
        const downloadPromises = fileChunks.chunks.map(async (chunkInfo) => {
            let chunkBuffer = null;
            // Try to download from available nodes
            for (const nodeId of chunkInfo.storageNodes) {
                try {
                    const client = this.clients.get(nodeId);
                    if (!client)
                        continue;
                    const stream = await client.getObject(this.config.defaultBucket, chunkInfo.id);
                    const buffers = [];
                    for await (const chunk of stream) {
                        buffers.push(chunk);
                    }
                    chunkBuffer = Buffer.concat(buffers);
                    // Verify chunk integrity
                    const downloadedChecksum = this.calculateChecksum(chunkBuffer);
                    if (downloadedChecksum !== chunkInfo.checksum) {
                        logger_1.default.warn(`Checksum mismatch for chunk ${chunkInfo.id} from node ${nodeId}`);
                        chunkBuffer = null;
                        continue;
                    }
                    logger_1.default.debug(`Downloaded chunk ${chunkInfo.id} from node ${nodeId}`);
                    break;
                }
                catch (error) {
                    logger_1.default.error(`Failed to download chunk ${chunkInfo.id} from node ${nodeId}:`, error);
                }
            }
            if (!chunkBuffer) {
                throw new Error(`Failed to download chunk ${chunkInfo.id} from any storage node`);
            }
            chunks[chunkInfo.index] = chunkBuffer;
        });
        await Promise.all(downloadPromises);
        const fileBuffer = Buffer.concat(chunks);
        // Verify file integrity
        const downloadedChecksum = this.calculateChecksum(fileBuffer);
        if (downloadedChecksum !== fileChunks.checksum) {
            throw new Error(`File integrity check failed for ${fileChunks.fileId}`);
        }
        logger_1.default.info(`Completed chunked download for file ${fileChunks.fileId}`);
        return fileBuffer;
    }
    async deleteFileChunked(fileChunks) {
        logger_1.default.info(`Starting chunked deletion for file ${fileChunks.fileId}`);
        const deletePromises = fileChunks.chunks.map(async (chunkInfo) => {
            const nodePromises = chunkInfo.storageNodes.map(async (nodeId) => {
                try {
                    const client = this.clients.get(nodeId);
                    if (!client)
                        return;
                    await client.removeObject(this.config.defaultBucket, chunkInfo.id);
                    logger_1.default.debug(`Deleted chunk ${chunkInfo.id} from node ${nodeId}`);
                }
                catch (error) {
                    logger_1.default.error(`Failed to delete chunk ${chunkInfo.id} from node ${nodeId}:`, error);
                }
            });
            await Promise.all(nodePromises);
        });
        await Promise.all(deletePromises);
        logger_1.default.info(`Completed chunked deletion for file ${fileChunks.fileId}`);
    }
    async getStorageStats() {
        const healthyNodes = this.getHealthyNodes();
        let totalSpace = 0;
        let usedSpace = 0;
        // This is a simplified implementation
        // In a real scenario, you'd query each node for actual storage statistics
        for (const node of healthyNodes) {
            try {
                const client = this.clients.get(node.id);
                if (!client)
                    continue;
                // For MinIO, we can't easily get storage stats without admin API
                // This is a placeholder implementation
                totalSpace += 1000000000000; // 1TB per node (placeholder)
                usedSpace += 100000000000; // 100GB used per node (placeholder)
            }
            catch (error) {
                logger_1.default.error(`Failed to get stats for node ${node.id}:`, error);
            }
        }
        return {
            totalNodes: this.storageNodes.size,
            healthyNodes: healthyNodes.length,
            totalSpace,
            usedSpace
        };
    }
    async listFiles(prefix) {
        const healthyNodes = this.getHealthyNodes();
        if (healthyNodes.length === 0) {
            throw new Error('No healthy storage nodes available');
        }
        // Use the first healthy node to list files
        const client = this.clients.get(healthyNodes[0].id);
        if (!client) {
            throw new Error('No available storage client');
        }
        const objects = [];
        const stream = client.listObjects(this.config.defaultBucket, prefix, true);
        return new Promise((resolve, reject) => {
            stream.on('data', (obj) => {
                if (obj.name) {
                    objects.push({
                        name: obj.name,
                        size: obj.size || 0,
                        etag: obj.etag || '',
                        lastModified: obj.lastModified || new Date()
                    });
                }
            });
            stream.on('error', reject);
            stream.on('end', () => resolve(objects));
        });
    }
    getConfig() {
        return this.config;
    }
    getClient(nodeId) {
        return this.clients.get(nodeId);
    }
    destroy() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        this.clients.clear();
        this.storageNodes.clear();
        logger_1.default.info('Storage service destroyed');
    }
}
exports.StorageService = StorageService;
// Default storage configuration
const defaultStorageConfig = {
    nodes: [
        {
            id: 'node-1',
            endPoint: process.env.MINIO_ENDPOINT || 'localhost',
            port: parseInt(process.env.MINIO_PORT || '9000', 10),
            useSSL: process.env.MINIO_USE_SSL === 'true',
            accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
            secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
            region: process.env.MINIO_REGION || 'us-east-1',
            isHealthy: true
        }
    ],
    defaultBucket: process.env.MINIO_BUCKET || 'cloud-storage',
    chunkSize: parseInt(process.env.CHUNK_SIZE || '5242880', 10), // 5MB chunks
    replicationFactor: parseInt(process.env.REPLICATION_FACTOR || '2', 10)
};
// Export singleton instance
exports.storageService = new StorageService(defaultStorageConfig);
exports.default = exports.storageService;
//# sourceMappingURL=storage.service.js.map