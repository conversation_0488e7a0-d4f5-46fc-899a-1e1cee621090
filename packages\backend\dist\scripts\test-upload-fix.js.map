{"version": 3, "file": "test-upload-fix.js", "sourceRoot": "", "sources": ["../../src/scripts/test-upload-fix.ts"], "names": [], "mappings": ";;;;;;AAEA,kDAA0B;AAC1B,0DAAiC;AACjC,4CAAoB;AACpB,gDAAwB;AAExB,KAAK,UAAU,aAAa;IAC1B,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAEhC,MAAM,OAAO,GAAG,uBAAuB,CAAC;IAExC,aAAa;IACb,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3B,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,OAAO,SAAS,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,EAAE,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO;IACT,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,YAAY,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;IAE3D,IAAI,CAAC;QACH,YAAE,CAAC,aAAa,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,gBAAgB,YAAY,EAAE,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;QACvC,OAAO;IACT,CAAC;IAED,gCAAgC;IAChC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,IAAI,CAAC;QACH,WAAW;QACX,MAAM,gBAAgB,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,OAAO,oBAAoB,EAAE;YACxE,KAAK,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,cAAc;YACvC,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;QAEH,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC/B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAExC,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,OAAO,iBAAiB,EAAE;gBAClE,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;YAEH,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,cAAc,UAAU,EAAE,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,OAAO;QACT,CAAC;IACH,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,mBAAQ,EAAE,CAAC;QAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,YAAE,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3D,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAErC,MAAM,cAAc,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,OAAO,mBAAmB,EAAE,QAAQ,EAAE;YAC/E,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,SAAS,EAAE;gBACtC,GAAG,QAAQ,CAAC,UAAU,EAAE;aACzB;YACD,OAAO,EAAE,KAAK,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YACzB,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC/B,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAC3C,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACnC,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;SAC5C,CAAC,CAAC;QAEH,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,YAAY,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,OAAO,iBAAiB,EAAE;YAChE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,SAAS,EAAE;aACvC;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAElE,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,MAAM,gBAAgB,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,OAAO,cAAc,MAAM,WAAW,EAAE;YAClF,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,SAAS,EAAE;aACvC;YACD,YAAY,EAAE,QAAQ;SACvB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;YACxB,WAAW,EAAE,gBAAgB,CAAC,OAAO,CAAC,cAAc,CAAC;YACrD,aAAa,EAAE,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC;YACzD,kBAAkB,EAAE,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC;SACpE,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAE9B,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAE7E,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;YAChF,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,EAAE,CAAC,CAAC;QACtC,CAAC;QACD,OAAO;IACT,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,CAAC;QACH,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC/B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACrC,CAAC;AAED,OAAO;AACP,aAAa,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}