{"name": "tsx", "version": "3.14.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "license": "MIT", "repository": "esbuild-kit/tsx", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "files": ["dist"], "exports": {"./package.json": "./package.json", ".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./esm": "./dist/esm/index.mjs", "./cli": "./dist/cli.mjs", "./source-map": "./dist/source-map.cjs", "./suppress-warnings": "./dist/suppress-warnings.cjs", "./preflight": "./dist/preflight.cjs", "./repl": "./dist/repl.mjs"}, "bin": "./dist/cli.mjs", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "optionalDependencies": {"fsevents": "~2.3.3"}}