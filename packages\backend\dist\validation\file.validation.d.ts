import Joi from 'joi';
export declare const fileValidation: {
    uploadFile: {
        body: Joi.ObjectSchema<any>;
    };
    uploadFiles: {
        body: Joi.ObjectSchema<any>;
    };
    downloadFile: {
        params: Joi.ObjectSchema<any>;
    };
    previewFile: {
        params: Joi.ObjectSchema<any>;
        query: Joi.ObjectSchema<any>;
    };
    listFiles: {
        query: Joi.ObjectSchema<any>;
    };
    searchFiles: {
        query: Joi.ObjectSchema<any>;
    };
    createFolder: {
        body: Joi.ObjectSchema<any>;
    };
    moveFile: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    deleteFile: {
        params: Joi.ObjectSchema<any>;
    };
    shareFile: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    getFileInfo: {
        params: Joi.ObjectSchema<any>;
    };
    initChunkedUpload: {
        body: Joi.ObjectSchema<any>;
    };
    uploadChunk: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    completeChunkedUpload: {
        params: Joi.ObjectSchema<any>;
    };
    getUploadProgress: {
        params: Joi.ObjectSchema<any>;
    };
    renameFolder: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    moveFolder: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    deleteFolder: {
        params: Joi.ObjectSchema<any>;
        query: Joi.ObjectSchema<any>;
    };
    renameFile: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    moveFileToTrash: {
        params: Joi.ObjectSchema<any>;
    };
    getTrashContents: {
        query: Joi.ObjectSchema<any>;
    };
    restoreFromTrash: {
        params: Joi.ObjectSchema<any>;
    };
    getFileShares: {
        params: Joi.ObjectSchema<any>;
    };
    updateShareLink: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
    revokeShareLink: {
        params: Joi.ObjectSchema<any>;
    };
    accessPublicShare: {
        params: Joi.ObjectSchema<any>;
        query: Joi.ObjectSchema<any>;
    };
    downloadPublicShare: {
        params: Joi.ObjectSchema<any>;
        body: Joi.ObjectSchema<any>;
    };
};
export default fileValidation;
//# sourceMappingURL=file.validation.d.ts.map