import { Credentials } from "./Credentials.mjs";
export class CredentialProvider {
  constructor({
    access<PERSON>ey,
    secretKey,
    sessionToken
  }) {
    this.credentials = new Credentials({
      accessKey,
      secretKey,
      sessionToken
    });
  }
  async getCredentials() {
    return this.credentials.get();
  }
  setCredentials(credentials) {
    if (credentials instanceof Credentials) {
      this.credentials = credentials;
    } else {
      throw new Error('Unable to set Credentials. it should be an instance of Credentials class');
    }
  }
  setAccessKey(accessKey) {
    this.credentials.setAccessKey(accessKey);
  }
  getAccessKey() {
    return this.credentials.getAccessKey();
  }
  setSecretKey(secretKey) {
    this.credentials.setSecretKey(secretKey);
  }
  getSecretKey() {
    return this.credentials.getSecretKey();
  }
  setSessionToken(sessionToken) {
    this.credentials.setSessionToken(sessionToken);
  }
  getSessionToken() {
    return this.credentials.getSessionToken();
  }
}

// deprecated default export, please use named exports.
// keep for backward compatibility.
// eslint-disable-next-line import/no-default-export
export default CredentialProvider;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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