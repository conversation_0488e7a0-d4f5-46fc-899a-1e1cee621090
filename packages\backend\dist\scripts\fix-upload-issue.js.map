{"version": 3, "file": "fix-upload-issue.js", "sourceRoot": "", "sources": ["../../src/scripts/fix-upload-issue.ts"], "names": [], "mappings": ";;;;;;AAEA,iCAA8C;AAC9C,uDAA+B;AAG/B,KAAK,UAAU,cAAc;IAC3B,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAElC,eAAe;IACf,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7B,MAAM,WAAW,GAAG,IAAI,cAAW,CAAC;QAClC,QAAQ,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ;QACvC,IAAI,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI;QAC/B,MAAM,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM;QACnC,SAAS,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;QACzC,SAAS,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;QACzC,MAAM,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM;KACpC,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,OAAO;QACP,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC;QAExD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,OAAO,CAAC,GAAG,CAAC,aAAa,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,KAAK,CAAC;IACf,CAAC;IAED,aAAa;IACb,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,MAAM,UAAU,GAAG,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;IAE/C,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,EAAE,CAAC,CAAC;YAC1C,MAAM,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,gBAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,OAAO,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,gBAAgB;QAChB,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,YAAY;YACrB,SAAS,EAAE;gBACT;oBACE,MAAM,EAAE,OAAO;oBACf,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;oBACzB,MAAM,EAAE,CAAC,cAAc,CAAC;oBACxB,QAAQ,EAAE,CAAC,gBAAgB,UAAU,IAAI,CAAC;iBAC3C;aACF;SACF,CAAC;QAEF,MAAM,WAAW,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAEhC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,EAAE,CAAC,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;QAC9C,MAAM,WAAW,GAAG,YAAY,CAAC;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEpD,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,EAAE,CAAC,CAAC;QAC7C,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE;YACnF,cAAc,EAAE,YAAY;YAC5B,iBAAiB,EAAE,MAAM;SAC1B,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,cAAc,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAE7E,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAC3B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YAClC,iBAAiB,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpC,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,IAAI,iBAAiB,KAAK,WAAW,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAE/B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IACnC,OAAO,IAAI,CAAC;AACd,CAAC;AAED,OAAO;AACP,cAAc,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;IAC9B,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;IACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}