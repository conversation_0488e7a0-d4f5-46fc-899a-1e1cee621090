#!/usr/bin/env tsx

import { createMongoDBFromEnv } from '../config/mongodb'
import { Collection, ObjectId } from 'mongodb'
import crypto from 'crypto'

interface FileDocument {
  _id: ObjectId
  fileId?: string
  userId: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  checksum: string
  folderId?: string
  isPublic: boolean
  uploadedAt: Date
  modifiedAt: Date
  tags: string[]
  storageNodes: any[]
  isDeleted?: boolean
  deletedAt?: Date
}

async function migrateFileIds() {
  console.log('Starting file ID migration...')
  
  try {
    const mongodb = createMongoDBFromEnv()
    await mongodb.connect()
    
    const collection: Collection<FileDocument> = mongodb.getCollection('file_metadata')
    
    // Find all files without fileId
    const filesWithoutFileId = await collection.find({ fileId: { $exists: false } }).toArray()
    
    console.log(`Found ${filesWithoutFileId.length} files without fileId`)
    
    if (filesWithoutFileId.length === 0) {
      console.log('No files need migration')
      return
    }
    
    // Update each file with a new UUID as fileId
    for (const file of filesWithoutFileId) {
      const fileId = crypto.randomUUID()
      
      await collection.updateOne(
        { _id: file._id },
        { $set: { fileId: fileId } }
      )
      
      console.log(`Updated file ${file._id} with fileId: ${fileId}`)
    }
    
    console.log(`Migration completed. Updated ${filesWithoutFileId.length} files.`)
    
    // Verify migration
    const remainingFiles = await collection.find({ fileId: { $exists: false } }).toArray()
    if (remainingFiles.length === 0) {
      console.log('✅ Migration verification successful - all files now have fileId')
    } else {
      console.log(`⚠️  Warning: ${remainingFiles.length} files still missing fileId`)
    }
    
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateFileIds()
    .then(() => {
      console.log('Migration script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Migration script failed:', error)
      process.exit(1)
    })
}

export { migrateFileIds }
