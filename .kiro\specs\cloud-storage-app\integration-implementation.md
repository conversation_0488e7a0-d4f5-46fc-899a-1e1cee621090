# 前后端对接具体实现方案

## 1. API客户端封装

### 1.1 创建统一的API客户端

首先需要在前端创建一个统一的API客户端来处理所有后端请求：

```typescript
// packages/frontend/src/services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ApiResponse, ErrorCode } from '@cloud-storage/shared';

class ApiClient {
  private client: AxiosInstance;
  private baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      withCredentials: true,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器 - 添加认证token
    this.client.interceptors.request.use((config) => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // 响应拦截器 - 处理错误和token刷新
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await this.handleTokenRefresh();
        }
        return Promise.reject(error);
      }
    );
  }

  private async handleTokenRefresh() {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) throw new Error('No refresh token');

      const response = await axios.post(`${this.baseURL}/api/auth/refresh`, {
        refreshToken
      });

      const { accessToken } = response.data.data;
      localStorage.setItem('accessToken', accessToken);
    } catch (error) {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      window.location.href = '/login';
    }
  }

  async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.request(config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### 1.2 创建具体的API服务

```typescript
// packages/frontend/src/services/authService.ts
import { apiClient } from './api';
import { LoginCredentials, UserRegistration, AuthResult } from '@cloud-storage/shared';

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    const response = await apiClient.request<AuthResult>({
      method: 'POST',
      url: '/api/auth/login',
      data: credentials,
    });
    return response.data!;
  },

  async register(userData: UserRegistration): Promise<AuthResult> {
    const response = await apiClient.request<AuthResult>({
      method: 'POST',
      url: '/api/auth/register',
      data: userData,
    });
    return response.data!;
  },

  async logout(): Promise<void> {
    await apiClient.request({
      method: 'POST',
      url: '/api/auth/logout',
    });
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  },

  async getCurrentUser() {
    const response = await apiClient.request({
      method: 'GET',
      url: '/api/auth/me',
    });
    return response.data;
  }
};
```

## 2. 状态管理集成

### 2.1 创建认证状态管理

```typescript
// packages/frontend/src/stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserInfo, AuthResult } from '@cloud-storage/shared';
import { authService } from '../services/authService';

interface AuthState {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: UserRegistration) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const result = await authService.login(credentials);
          localStorage.setItem('accessToken', result.tokens.accessToken);
          localStorage.setItem('refreshToken', result.tokens.refreshToken);
          set({ 
            user: result.user, 
            isAuthenticated: true, 
            isLoading: false 
          });
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error?.message || '登录失败', 
            isLoading: false 
          });
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null });
        try {
          const result = await authService.register(userData);
          localStorage.setItem('accessToken', result.tokens.accessToken);
          localStorage.setItem('refreshToken', result.tokens.refreshToken);
          set({ 
            user: result.user, 
            isAuthenticated: true, 
            isLoading: false 
          });
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error?.message || '注册失败', 
            isLoading: false 
          });
        }
      },

      logout: async () => {
        try {
          await authService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({ user: null, isAuthenticated: false });
        }
      },

      checkAuth: async () => {
        const token = localStorage.getItem('accessToken');
        if (!token) return;

        try {
          const user = await authService.getCurrentUser();
          set({ user, isAuthenticated: true });
        } catch (error) {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          set({ user: null, isAuthenticated: false });
        }
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);
```

### 2.2 创建文件管理状态

```typescript
// packages/frontend/src/stores/fileStore.ts
import { create } from 'zustand';
import { FileMetadata, FolderInfo } from '@cloud-storage/shared';
import { fileService } from '../services/fileService';

interface FileState {
  files: FileMetadata[];
  currentFolder: FolderInfo | null;
  isLoading: boolean;
  uploadProgress: Record<string, number>;
  error: string | null;

  // Actions
  loadFiles: (folderId?: string) => Promise<void>;
  uploadFile: (file: File, folderId?: string) => Promise<void>;
  deleteFile: (fileId: string) => Promise<void>;
  createFolder: (name: string, parentId?: string) => Promise<void>;
  moveFile: (fileId: string, targetFolderId: string) => Promise<void>;
}

export const useFileStore = create<FileState>((set, get) => ({
  files: [],
  currentFolder: null,
  isLoading: false,
  uploadProgress: {},
  error: null,

  loadFiles: async (folderId) => {
    set({ isLoading: true, error: null });
    try {
      const files = await fileService.getFiles(folderId);
      set({ files, isLoading: false });
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error?.message || '加载文件失败', 
        isLoading: false 
      });
    }
  },

  uploadFile: async (file, folderId) => {
    const fileId = Date.now().toString();
    set(state => ({
      uploadProgress: { ...state.uploadProgress, [fileId]: 0 }
    }));

    try {
      await fileService.uploadFile(file, folderId, (progress) => {
        set(state => ({
          uploadProgress: { ...state.uploadProgress, [fileId]: progress }
        }));
      });
      
      // 上传完成后重新加载文件列表
      await get().loadFiles(folderId);
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || '上传失败' });
    } finally {
      set(state => {
        const { [fileId]: _, ...rest } = state.uploadProgress;
        return { uploadProgress: rest };
      });
    }
  },

  deleteFile: async (fileId) => {
    try {
      await fileService.deleteFile(fileId);
      set(state => ({
        files: state.files.filter(file => file.id !== fileId)
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || '删除失败' });
    }
  },

  createFolder: async (name, parentId) => {
    try {
      await fileService.createFolder(name, parentId);
      await get().loadFiles(parentId);
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || '创建文件夹失败' });
    }
  },

  moveFile: async (fileId, targetFolderId) => {
    try {
      await fileService.moveFile(fileId, targetFolderId);
      await get().loadFiles();
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || '移动文件失败' });
    }
  },
}));
```

## 3. WebSocket集成

### 3.1 创建WebSocket服务

```typescript
// packages/frontend/src/services/websocketService.ts
import { io, Socket } from 'socket.io-client';
import { SyncEvent, SyncStatus } from '@cloud-storage/shared';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect() {
    const token = localStorage.getItem('accessToken');
    if (!token) return;

    this.socket = io('http://localhost:3001', {
      auth: { token },
      transports: ['websocket'],
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      this.handleReconnect();
    });

    this.socket.on('sync:event', (event: SyncEvent) => {
      // 处理同步事件
      this.handleSyncEvent(event);
    });

    this.socket.on('sync:status', (status: SyncStatus) => {
      // 更新同步状态
      this.handleSyncStatus(status);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect();
      }, Math.pow(2, this.reconnectAttempts) * 1000);
    }
  }

  private handleSyncEvent(event: SyncEvent) {
    // 通知相关组件更新
    window.dispatchEvent(new CustomEvent('sync:event', { detail: event }));
  }

  private handleSyncStatus(status: SyncStatus) {
    // 更新同步状态
    window.dispatchEvent(new CustomEvent('sync:status', { detail: status }));
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  emit(event: string, data: any) {
    if (this.socket) {
      this.socket.emit(event, data);
    }
  }
}

export const websocketService = new WebSocketService();
```

### 3.2 创建同步状态管理

```typescript
// packages/frontend/src/stores/syncStore.ts
import { create } from 'zustand';
import { SyncStatus, SyncEvent } from '@cloud-storage/shared';
import { websocketService } from '../services/websocketService';

interface SyncState {
  status: SyncStatus | null;
  events: SyncEvent[];
  isConnected: boolean;
  
  // Actions
  connect: () => void;
  disconnect: () => void;
  updateStatus: (status: SyncStatus) => void;
  addEvent: (event: SyncEvent) => void;
}

export const useSyncStore = create<SyncState>((set, get) => ({
  status: null,
  events: [],
  isConnected: false,

  connect: () => {
    websocketService.connect();
    set({ isConnected: true });

    // 监听同步事件
    window.addEventListener('sync:event', (e: any) => {
      get().addEvent(e.detail);
    });

    window.addEventListener('sync:status', (e: any) => {
      get().updateStatus(e.detail);
    });
  },

  disconnect: () => {
    websocketService.disconnect();
    set({ isConnected: false });
  },

  updateStatus: (status) => {
    set({ status });
  },

  addEvent: (event) => {
    set(state => ({
      events: [event, ...state.events.slice(0, 99)] // 保留最近100个事件
    }));
  },
}));
```

## 4. 组件更新

### 4.1 更新登录组件

```typescript
// packages/frontend/src/pages/Login.tsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { LoginCredentials } from '@cloud-storage/shared';

export const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading, error, clearError } = useAuthStore();
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
    rememberMe: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    try {
      await login(credentials);
      navigate('/dashboard');
    } catch (error) {
      // 错误已在store中处理
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              邮箱
            </label>
            <input
              type="email"
              required
              value={credentials.email}
              onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              密码
            </label>
            <input
              type="password"
              required
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={credentials.rememberMe}
              onChange={(e) => setCredentials(prev => ({ ...prev, rememberMe: e.target.checked }))}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
              记住我
            </label>
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoading ? '登录中...' : '登录'}
          </button>
        </form>
      </div>
    </div>
  );
};
```

### 4.2 更新文件列表组件

```typescript
// packages/frontend/src/components/files/FileList.tsx
import React, { useEffect } from 'react';
import { useFileStore } from '../../stores/fileStore';
import { useSyncStore } from '../../stores/syncStore';

export const FileList: React.FC = () => {
  const { 
    files, 
    isLoading, 
    uploadProgress, 
    error, 
    loadFiles, 
    uploadFile, 
    deleteFile 
  } = useFileStore();
  
  const { connect, status } = useSyncStore();

  useEffect(() => {
    loadFiles();
    connect(); // 连接WebSocket
  }, []);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        uploadFile(file);
      });
    }
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  return (
    <div className="p-6">
      {/* 同步状态指示器 */}
      {status && (
        <div className="mb-4 p-2 bg-blue-100 dark:bg-blue-900 rounded">
          同步状态: {status.status} - {status.message}
        </div>
      )}

      {/* 上传区域 */}
      <div className="mb-6">
        <input
          type="file"
          multiple
          onChange={handleFileUpload}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"
        />
      </div>

      {/* 上传进度 */}
      {Object.entries(uploadProgress).map(([fileId, progress]) => (
        <div key={fileId} className="mb-2">
          <div className="bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="text-sm text-gray-600">{progress}%</span>
        </div>
      ))}

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* 文件列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {files.map(file => (
          <div key={file.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {file.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
                <p className="text-xs text-gray-400">
                  {new Date(file.createdAt).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={() => deleteFile(file.id)}
                className="text-red-600 hover:text-red-800 text-sm"
              >
                删除
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 5. 环境配置

### 5.1 前端环境变量

```env
# packages/frontend/.env
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001
VITE_CDN_URL=http://localhost:9000
```

### 5.2 后端CORS配置

```typescript
// packages/backend/src/app.ts 中添加CORS配置
import cors from 'cors';

app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));
```

## 6. 启动脚本

更新根目录的package.json脚本：

```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:shared\"",
    "dev:backend": "npm run dev --workspace=@cloud-storage/backend",
    "dev:frontend": "npm run dev --workspace=@cloud-storage/frontend",
    "dev:shared": "npm run dev --workspace=@cloud-storage/shared",
    "build": "npm run build --workspace=@cloud-storage/shared && npm run build --workspaces --if-present",
    "start": "npm run start --workspace=@cloud-storage/backend"
  }
}
```

这个实现方案提供了完整的前后端对接架构，包括API客户端、状态管理、WebSocket集成和组件更新。按照这个方案实施，可以实现前后端的无缝对接。