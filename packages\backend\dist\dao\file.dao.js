"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileDao = exports.FileDAO = void 0;
const file_metadata_model_1 = require("../models/file-metadata.model");
const folder_model_1 = require("../models/folder.model");
const share_link_model_1 = require("../models/share-link.model");
const crypto = __importStar(require("crypto"));
class FileDAO {
    constructor(mongodb) {
        this.fileMetadataModel = new file_metadata_model_1.FileMetadataModel(mongodb);
        this.folderModel = new folder_model_1.FolderModel(mongodb);
        this.shareLinkModel = new share_link_model_1.ShareLinkModel(mongodb);
    }
    // File Metadata Operations
    async createFile(fileMetadata) {
        return await this.fileMetadataModel.create(fileMetadata);
    }
    async getFileById(id) {
        return await this.fileMetadataModel.findById(id);
    }
    async getFilesByUserId(userId, folderId) {
        return await this.fileMetadataModel.findByUserId(userId, folderId);
    }
    async getFilesByUser(userId, folderId, pagination = { page: 1, limit: 50 }) {
        let files = await this.fileMetadataModel.findByUserId(userId, folderId);
        // Apply file type filter if specified
        if (pagination.fileType) {
            files = this.filterFilesByType(files, pagination.fileType);
        }
        // Apply sorting
        if (pagination.sortBy) {
            files = this.sortFiles(files, pagination.sortBy, pagination.sortOrder || 'asc');
        }
        const totalCount = files.length;
        const startIndex = (pagination.page - 1) * pagination.limit;
        const endIndex = startIndex + pagination.limit;
        return {
            files: files.slice(startIndex, endIndex),
            totalCount
        };
    }
    filterFilesByType(files, fileType) {
        switch (fileType.toLowerCase()) {
            case 'image':
            case 'images':
                return files.filter(file => file.mimeType.startsWith('image/'));
            case 'video':
            case 'videos':
                return files.filter(file => file.mimeType.startsWith('video/'));
            case 'audio':
                return files.filter(file => file.mimeType.startsWith('audio/'));
            case 'document':
            case 'documents':
                return files.filter(file => file.mimeType.includes('pdf') ||
                    file.mimeType.includes('word') ||
                    file.mimeType.includes('document') ||
                    file.mimeType.includes('text') ||
                    file.mimeType.includes('spreadsheet') ||
                    file.mimeType.includes('presentation'));
            case 'archive':
            case 'archives':
                return files.filter(file => file.mimeType.includes('zip') ||
                    file.mimeType.includes('rar') ||
                    file.mimeType.includes('tar') ||
                    file.mimeType.includes('gzip'));
            default:
                // If specific mime type is provided
                return files.filter(file => file.mimeType.includes(fileType));
        }
    }
    sortFiles(files, sortBy, sortOrder) {
        return files.sort((a, b) => {
            let comparison = 0;
            switch (sortBy) {
                case 'name':
                    comparison = a.originalName.localeCompare(b.originalName);
                    break;
                case 'size':
                    comparison = a.size - b.size;
                    break;
                case 'modifiedAt':
                    comparison = a.modifiedAt.getTime() - b.modifiedAt.getTime();
                    break;
                case 'uploadedAt':
                    comparison = a.uploadedAt.getTime() - b.uploadedAt.getTime();
                    break;
                case 'type':
                    comparison = a.mimeType.localeCompare(b.mimeType);
                    break;
                default:
                    comparison = a.originalName.localeCompare(b.originalName);
            }
            return sortOrder === 'desc' ? -comparison : comparison;
        });
    }
    async getFoldersByParent(userId, parentId) {
        return await this.folderModel.findByUserId(userId, parentId);
    }
    async getRootFolders(userId) {
        return await this.folderModel.findByUserId(userId, undefined);
    }
    async searchFiles(userId, query) {
        let files;
        if (query.tags && query.tags.length > 0) {
            files = await this.fileMetadataModel.findByTags(userId, query.tags);
        }
        else {
            files = await this.fileMetadataModel.search(userId, query.query, query.folderId);
        }
        // Apply file type filter if specified
        if (query.fileType) {
            files = this.filterFilesByType(files, query.fileType);
        }
        // Apply date range filter if specified
        if (query.dateRange) {
            files = files.filter(file => {
                const fileDate = file.modifiedAt;
                return fileDate >= query.dateRange.from && fileDate <= query.dateRange.to;
            });
        }
        return {
            files,
            totalCount: files.length
        };
    }
    async updateFile(id, updates) {
        return await this.fileMetadataModel.update(id, updates);
    }
    async deleteFile(id) {
        // Also delete associated share links
        const file = await this.fileMetadataModel.findById(id);
        if (file) {
            await this.shareLinkModel.revokeByFileId(id);
        }
        return await this.fileMetadataModel.delete(id);
    }
    async getFileByChecksum(checksum) {
        return await this.fileMetadataModel.findByChecksum(checksum);
    }
    async getUserStorageStats(userId) {
        return await this.fileMetadataModel.getStorageStats(userId);
    }
    // File Chunks Operations (stored in MongoDB for now)
    async saveFileChunks(fileId, fileChunks) {
        const collection = this.fileMetadataModel.getCollection('file_chunks');
        await collection.insertOne({
            fileId,
            ...fileChunks,
            createdAt: new Date()
        });
    }
    async getFileChunks(fileId) {
        const collection = this.fileMetadataModel.getCollection('file_chunks');
        const result = await collection.findOne({ fileId });
        return result ? {
            fileId: result.fileId,
            totalChunks: result.totalChunks,
            chunks: result.chunks,
            totalSize: result.totalSize,
            checksum: result.checksum
        } : null;
    }
    async deleteFileChunks(fileId) {
        const collection = this.fileMetadataModel.getCollection('file_chunks');
        const result = await collection.deleteOne({ fileId });
        return result.deletedCount > 0;
    }
    // Folder Operations
    async createFolder(folder) {
        return await this.folderModel.create(folder);
    }
    async getFolderById(id) {
        return await this.folderModel.findById(id);
    }
    async getFoldersByUserId(userId, parentId) {
        return await this.folderModel.findByUserId(userId, parentId);
    }
    async getFolderByPath(userId, path) {
        return await this.folderModel.findByPath(userId, path);
    }
    async updateFolder(id, updates) {
        return await this.folderModel.update(id, updates);
    }
    async deleteFolder(id) {
        // TODO: In a complete implementation, we should also delete all files and subfolders
        return await this.folderModel.delete(id);
    }
    async moveFolder(id, newParentId) {
        const folder = await this.folderModel.findById(id);
        if (!folder)
            return false;
        const newPath = await this.folderModel.buildPath(folder.userId, newParentId);
        const fullNewPath = newPath === '/' ? `/${folder.name}` : `${newPath}/${folder.name}`;
        return await this.folderModel.move(id, newParentId, fullNewPath);
    }
    async renameFolder(id, newName) {
        return await this.folderModel.rename(id, newName);
    }
    async validateFolderName(userId, parentId, name) {
        return await this.folderModel.validatePath(userId, parentId, name);
    }
    // Share Link Operations
    async createShareLink(shareLink) {
        return await this.shareLinkModel.create(shareLink);
    }
    async getShareLinkById(id) {
        return await this.shareLinkModel.findById(id);
    }
    async getShareLinkByToken(token) {
        return await this.shareLinkModel.findByToken(token);
    }
    async getShareLinksByFileId(fileId) {
        return await this.shareLinkModel.findByFileId(fileId);
    }
    async getShareLinksByUserId(userId) {
        return await this.shareLinkModel.findByUserId(userId);
    }
    async updateShareLink(id, updates) {
        return await this.shareLinkModel.update(id, updates);
    }
    async deleteShareLink(id) {
        return await this.shareLinkModel.delete(id);
    }
    async validateShareLink(token, password) {
        const validation = await this.shareLinkModel.isValidForDownload(token);
        if (!validation.valid || !validation.shareLink) {
            return validation;
        }
        // Check password if required
        if (validation.shareLink.password) {
            if (!password) {
                return { valid: false, reason: 'Password required' };
            }
            const passwordValid = await this.shareLinkModel.validatePassword(token, password);
            if (!passwordValid) {
                return { valid: false, reason: 'Invalid password' };
            }
        }
        return validation;
    }
    async recordDownload(shareId) {
        return await this.shareLinkModel.incrementDownloadCount(shareId);
    }
    // Combined Operations
    async getFileList(userId, folderId, pagination) {
        const [files, folders] = await Promise.all([
            this.getFilesByUserId(userId, folderId),
            this.getFoldersByUserId(userId, folderId)
        ]);
        // Apply pagination if provided
        let paginatedFiles = files;
        let hasMore = false;
        if (pagination) {
            const startIndex = (pagination.page - 1) * pagination.limit;
            const endIndex = startIndex + pagination.limit;
            paginatedFiles = files.slice(startIndex, endIndex);
            hasMore = files.length > endIndex;
        }
        return {
            files: paginatedFiles,
            folders,
            totalCount: files.length + folders.length,
            hasMore
        };
    }
    async createFolderWithValidation(userId, folderData) {
        // Validate folder name uniqueness
        const isValid = await this.validateFolderName(userId, folderData.parentId, folderData.name);
        if (!isValid) {
            throw new Error('Folder name already exists in this location');
        }
        // Build the full path
        const path = await this.folderModel.buildPath(userId, folderData.parentId);
        const fullPath = path === '/' ? `/${folderData.name}` : `${path}/${folderData.name}`;
        const folder = {
            id: crypto.randomUUID(),
            userId,
            name: folderData.name,
            parentId: folderData.parentId,
            path: fullPath,
            createdAt: new Date(),
            modifiedAt: new Date()
        };
        return await this.createFolder(folder);
    }
    async shareFileWithOptions(fileId, userId, options) {
        const shareData = {
            id: crypto.randomUUID(),
            fileId,
            userId,
            token: crypto.randomBytes(32).toString('hex'),
            permissions: options.permissions,
            expiresAt: options.expiresAt,
            password: options.password,
            downloadCount: 0,
            maxDownloads: options.maxDownloads,
            createdAt: new Date()
        };
        return await this.createShareLink(shareData);
    }
    // Cleanup Operations
    async cleanupExpiredShares() {
        return await this.shareLinkModel.cleanupExpired();
    }
    // Statistics
    async getUserShareStats(userId) {
        return await this.shareLinkModel.getShareStats(userId);
    }
    // Trash/Recycle Bin Operations
    async getDeletedFiles(userId, pagination) {
        const files = await this.fileMetadataModel.findDeleted(userId);
        const startIndex = (pagination.page - 1) * pagination.limit;
        const endIndex = startIndex + pagination.limit;
        return {
            files: files.slice(startIndex, endIndex),
            totalCount: files.length
        };
    }
    async getDeletedFolders(userId) {
        return await this.folderModel.findDeleted(userId);
    }
    async restoreFile(id) {
        return await this.fileMetadataModel.restore(id);
    }
    async restoreFolder(id) {
        return await this.folderModel.restore(id);
    }
}
exports.FileDAO = FileDAO;
// Create and export singleton instance
const mongodb_1 = require("../config/mongodb");
const mongodb = (0, mongodb_1.createMongoDBFromEnv)();
exports.fileDao = new FileDAO(mongodb);
//# sourceMappingURL=file.dao.js.map