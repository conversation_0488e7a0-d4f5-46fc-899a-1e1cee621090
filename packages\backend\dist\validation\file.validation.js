"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileValidation = void 0;
const joi_1 = __importDefault(require("joi"));
exports.fileValidation = {
    uploadFile: {
        body: joi_1.default.object({
            folderId: joi_1.default.string().uuid().optional(),
            tags: joi_1.default.string().optional(), // JSON string of tags array
            isPublic: joi_1.default.string().valid('true', 'false').optional()
        })
    },
    uploadFiles: {
        body: joi_1.default.object({
            folderId: joi_1.default.string().uuid().optional(),
            tags: joi_1.default.string().optional(), // JSON string of tags array
            isPublic: joi_1.default.string().valid('true', 'false').optional()
        })
    },
    downloadFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    previewFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        }),
        query: joi_1.default.object({
            quality: joi_1.default.string().valid('low', 'medium', 'high').optional(),
            maxWidth: joi_1.default.number().integer().min(100).max(2000).optional(),
            maxHeight: joi_1.default.number().integer().min(100).max(2000).optional()
        })
    },
    listFiles: {
        query: joi_1.default.object({
            folderId: joi_1.default.string().uuid().optional(),
            page: joi_1.default.number().integer().min(1).optional(),
            limit: joi_1.default.number().integer().min(1).max(100).optional(),
            sortBy: joi_1.default.string().valid('name', 'size', 'uploadedAt', 'modifiedAt', 'type').optional(),
            sortOrder: joi_1.default.string().valid('asc', 'desc').optional(),
            fileType: joi_1.default.string().valid('image', 'video', 'audio', 'document', 'archive').optional()
        })
    },
    searchFiles: {
        query: joi_1.default.object({
            q: joi_1.default.string().min(1).required(),
            fileType: joi_1.default.string().optional(),
            folderId: joi_1.default.string().uuid().optional(),
            tags: joi_1.default.string().optional(), // Comma-separated tags
            from: joi_1.default.date().iso().optional(),
            to: joi_1.default.date().iso().optional()
        })
    },
    createFolder: {
        body: joi_1.default.object({
            name: joi_1.default.string().min(1).max(255).required(),
            parentId: joi_1.default.string().uuid().optional()
        })
    },
    moveFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            targetFolderId: joi_1.default.string().uuid().optional().allow(null)
        })
    },
    deleteFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    shareFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            permissions: joi_1.default.array().items(joi_1.default.object({
                type: joi_1.default.string().valid('read', 'write', 'delete').required(),
                granted: joi_1.default.boolean().required()
            })).optional(),
            expiresAt: joi_1.default.date().iso().optional(),
            password: joi_1.default.string().min(4).optional(),
            maxDownloads: joi_1.default.number().integer().min(1).optional()
        })
    },
    getFileInfo: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    initChunkedUpload: {
        body: joi_1.default.object({
            filename: joi_1.default.string().min(1).max(255).required(),
            fileSize: joi_1.default.number().integer().min(1).max(500 * 1024 * 1024).required(), // 500MB max
            mimeType: joi_1.default.string().required(),
            totalChunks: joi_1.default.number().integer().min(1).max(1000).required(),
            folderId: joi_1.default.string().uuid().optional(),
            tags: joi_1.default.array().items(joi_1.default.string()).optional()
        })
    },
    uploadChunk: {
        params: joi_1.default.object({
            uploadId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            chunkIndex: joi_1.default.number().integer().min(0).required()
        })
    },
    completeChunkedUpload: {
        params: joi_1.default.object({
            uploadId: joi_1.default.string().uuid().required()
        })
    },
    getUploadProgress: {
        params: joi_1.default.object({
            uploadId: joi_1.default.string().uuid().required()
        })
    },
    renameFolder: {
        params: joi_1.default.object({
            folderId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            name: joi_1.default.string().min(1).max(255).required()
        })
    },
    moveFolder: {
        params: joi_1.default.object({
            folderId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            targetFolderId: joi_1.default.string().uuid().optional().allow(null)
        })
    },
    deleteFolder: {
        params: joi_1.default.object({
            folderId: joi_1.default.string().uuid().required()
        }),
        query: joi_1.default.object({
            permanent: joi_1.default.string().valid('true', 'false').optional()
        })
    },
    renameFile: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            name: joi_1.default.string().min(1).max(255).required()
        })
    },
    moveFileToTrash: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    getTrashContents: {
        query: joi_1.default.object({
            page: joi_1.default.number().integer().min(1).optional(),
            limit: joi_1.default.number().integer().min(1).max(100).optional(),
            sortBy: joi_1.default.string().valid('name', 'size', 'deletedAt', 'modifiedAt').optional(),
            sortOrder: joi_1.default.string().valid('asc', 'desc').optional()
        })
    },
    restoreFromTrash: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    getFileShares: {
        params: joi_1.default.object({
            fileId: joi_1.default.string().uuid().required()
        })
    },
    updateShareLink: {
        params: joi_1.default.object({
            shareId: joi_1.default.string().uuid().required()
        }),
        body: joi_1.default.object({
            permissions: joi_1.default.array().items(joi_1.default.object({
                type: joi_1.default.string().valid('read', 'write', 'delete').required(),
                granted: joi_1.default.boolean().required()
            })).optional(),
            expiresAt: joi_1.default.date().iso().optional().allow(null),
            password: joi_1.default.string().min(4).optional().allow(null),
            maxDownloads: joi_1.default.number().integer().min(1).optional().allow(null)
        })
    },
    revokeShareLink: {
        params: joi_1.default.object({
            shareId: joi_1.default.string().uuid().required()
        })
    },
    accessPublicShare: {
        params: joi_1.default.object({
            token: joi_1.default.string().length(64).required()
        }),
        query: joi_1.default.object({
            password: joi_1.default.string().optional()
        })
    },
    downloadPublicShare: {
        params: joi_1.default.object({
            token: joi_1.default.string().length(64).required()
        }),
        body: joi_1.default.object({
            password: joi_1.default.string().optional()
        })
    }
};
exports.default = exports.fileValidation;
//# sourceMappingURL=file.validation.js.map