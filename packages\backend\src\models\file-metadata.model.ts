import { Collection, ObjectId, WithId } from 'mongodb'
import { MongoDB } from '../config/mongodb'
import { FileMetadata, StorageNode } from '@cloud-storage/shared'

export interface FileMetadataDocument extends Omit<FileMetadata, 'id'> {
  _id?: ObjectId
  fileId: string  // Store the original UUID here
  isDeleted?: boolean
  deletedAt?: Date
}

export class FileMetadataModel {
  private collection: Collection<FileMetadataDocument>
  private mongodb: MongoDB

  constructor(mongodb: MongoDB) {
    this.mongodb = mongodb
    this.collection = mongodb.getCollection<FileMetadataDocument>('file_metadata')
  }

  getCollection(name: string): Collection {
    return this.mongodb.getCollection(name)
  }

  async create(fileMetadata: FileMetadata): Promise<FileMetadata> {
    const document: FileMetadataDocument = {
      ...fileMetadata,
      fileId: fileMetadata.id,  // Store the UUID in fileId field
      uploadedAt: new Date(fileMetadata.uploadedAt),
      modifiedAt: new Date(fileMetadata.modifiedAt)
    }
    // Remove the id field since MongoDB will generate _id
    delete (document as any).id

    const result = await this.collection.insertOne(document)

    return {
      ...fileMetadata,
      id: fileMetadata.id  // Return the original UUID
    }
  }

  async findById(id: string): Promise<FileMetadata | null> {
    try {
      // First try to find by fileId (UUID)
      let document = await this.collection.findOne({ fileId: id })

      // If not found and id looks like ObjectId, try _id
      if (!document && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
        document = await this.collection.findOne({ _id: new ObjectId(id) })
      }

      return document ? this.documentToFileMetadata(document) : null
    } catch (error) {
      return null
    }
  }

  async findByUserId(userId: string, folderId?: string): Promise<FileMetadata[]> {
    const filter: any = { userId, isDeleted: { $ne: true } }
    if (folderId !== undefined) {
      filter.folderId = folderId
    }

    const documents = await this.collection.find(filter).sort({ uploadedAt: -1 }).toArray()
    return documents.map(doc => this.documentToFileMetadata(doc))
  }

  async findByChecksum(checksum: string): Promise<FileMetadata | null> {
    const document = await this.collection.findOne({ checksum })
    return document ? this.documentToFileMetadata(document) : null
  }

  async search(userId: string, query: string, folderId?: string): Promise<FileMetadata[]> {
    const filter: any = {
      userId,
      isDeleted: { $ne: true },
      $or: [
        { filename: { $regex: query, $options: 'i' } },
        { originalName: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } }
      ]
    }

    if (folderId !== undefined) {
      filter.folderId = folderId
    }

    const documents = await this.collection.find(filter).sort({ uploadedAt: -1 }).toArray()
    return documents.map(doc => this.documentToFileMetadata(doc))
  }

  async update(id: string, updates: Partial<Omit<FileMetadata, 'id'>>): Promise<boolean> {
    try {
      const updateDoc: any = { ...updates }
      if (updates.modifiedAt) {
        updateDoc.modifiedAt = new Date(updates.modifiedAt)
      }

      // Try to update by fileId first, then by _id
      let result = await this.collection.updateOne(
        { fileId: id },
        { $set: updateDoc }
      )

      // If not found and id looks like ObjectId, try _id
      if (result.matchedCount === 0 && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          { $set: updateDoc }
        )
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      // Try to delete by fileId first, then by _id
      let result = await this.collection.deleteOne({ fileId: id })

      // If not found and id looks like ObjectId, try _id
      if (result.deletedCount === 0 && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.deleteOne({ _id: new ObjectId(id) })
      }

      return result.deletedCount > 0
    } catch (error) {
      return false
    }
  }

  async updateStorageNodes(id: string, storageNodes: StorageNode[]): Promise<boolean> {
    try {
      // Try to update by fileId first, then by _id
      let result = await this.collection.updateOne(
        { fileId: id },
        { $set: { storageNodes, modifiedAt: new Date() } }
      )

      // If not found and id looks like ObjectId, try _id
      if (result.matchedCount === 0 && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          { $set: { storageNodes, modifiedAt: new Date() } }
        )
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async findByTags(userId: string, tags: string[]): Promise<FileMetadata[]> {
    const documents = await this.collection.find({
      userId,
      isDeleted: { $ne: true },
      tags: { $in: tags }
    }).sort({ uploadedAt: -1 }).toArray()
    
    return documents.map(doc => this.documentToFileMetadata(doc))
  }

  async findDeleted(userId: string): Promise<FileMetadata[]> {
    const documents = await this.collection.find({
      userId,
      isDeleted: true
    }).sort({ deletedAt: -1 }).toArray()
    
    return documents.map(doc => this.documentToFileMetadata(doc))
  }

  async restore(id: string): Promise<boolean> {
    try {
      // Try to restore by fileId first, then by _id
      let result = await this.collection.updateOne(
        { fileId: id },
        {
          $unset: { isDeleted: "", deletedAt: "" },
          $set: { modifiedAt: new Date() }
        }
      )

      // If not found and id looks like ObjectId, try _id
      if (result.matchedCount === 0 && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          {
            $unset: { isDeleted: "", deletedAt: "" },
            $set: { modifiedAt: new Date() }
          }
        )
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async getStorageStats(userId: string): Promise<{ totalSize: number; fileCount: number }> {
    const pipeline = [
      { $match: { userId, isDeleted: { $ne: true } } },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$size' },
          fileCount: { $sum: 1 }
        }
      }
    ]

    const result = await this.collection.aggregate(pipeline).toArray()
    return result.length > 0 
      ? { totalSize: result[0].totalSize, fileCount: result[0].fileCount }
      : { totalSize: 0, fileCount: 0 }
  }

  private documentToFileMetadata(document: WithId<FileMetadataDocument>): FileMetadata {
    return {
      id: document.fileId || document._id.toString(), // Use fileId if available, fallback to _id
      userId: document.userId,
      filename: document.filename,
      originalName: document.originalName,
      mimeType: document.mimeType,
      size: document.size,
      checksum: document.checksum,
      folderId: document.folderId,
      isPublic: document.isPublic,
      uploadedAt: document.uploadedAt,
      modifiedAt: document.modifiedAt,
      tags: document.tags,
      storageNodes: document.storageNodes,
      isDeleted: document.isDeleted,
      deletedAt: document.deletedAt
    }
  }
}