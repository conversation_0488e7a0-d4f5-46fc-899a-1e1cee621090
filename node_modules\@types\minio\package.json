{"name": "@types/minio", "version": "7.1.0", "description": "TypeScript definitions for minio", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minio", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/barinbritva", "githubUsername": "bar<PERSON>brit<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/castorw", "githubUsername": "castorw"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/loremaps", "githubUsername": "loremaps"}, {"name": "<PERSON>", "url": "https://github.com/OutdatedVersion", "githubUsername": "OutdatedVersion"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seohyun0120", "githubUsername": "seohyun0120"}, {"name": "Trim21", "url": "https://github.com/trim21", "githubUsername": "trim21"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minio"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "946994b096be4d21329eab0e7d791ac920a0173d9d3e0723ba6f55c560a2fe41", "typeScriptVersion": "4.3"}